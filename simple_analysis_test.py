#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的快递上传分析测试脚本
验证从IDA分析中提取的关键信息（无需外部依赖）
"""

import json
import hashlib

def test_operation_types():
    """测试四种操作类型配置"""
    print("=" * 60)
    print("测试四种快递操作类型")
    print("=" * 60)
    
    # 从IDA分析中提取的四种操作类型
    operation_types = {
        "arrival": {      # 到达
            "app_type": "XCRK",
            "operation_code": 24,
            "packet_type": "2(0)",
            "description": "快递到达网点"
        },
        "package": {      # 建包
            "app_type": "wddpyt", 
            "operation_code": 13,
            "packet_type": "2(1)",
            "description": "建立包裹"
        },
        "town": {         # 乡镇
            "app_type": "wddpyt",
            "operation_code": 13, 
            "packet_type": "2(1)",
            "description": "乡镇配送"
        },
        "delivery": {     # 到派
            "app_type": "wddpyt",
            "operation_code": 13,
            "packet_type": "2(1)",
            "description": "派送操作"
        }
    }
    
    for op_name, config in operation_types.items():
        print(f"\n{op_name.upper()} 操作:")
        print(f"  应用类型: {config['app_type']}")
        print(f"  操作码: {config['operation_code']}")
        print(f"  数据包类型: {config['packet_type']}")
        print(f"  说明: {config['description']}")
    
    print("\n✓ 四种操作类型配置验证完成")

def test_packet_structure():
    """测试数据包结构"""
    print("\n" + "=" * 60)
    print("测试数据包结构")
    print("=" * 60)
    
    tracking_number = "YT1234567890123"
    employee_id = "9528"
    timestamp = "1640995200000"
    
    # 测试2(0)类型数据包（到达操作）
    print("\n--- 2(0) 数据包结构 (到达操作) ---")
    packet_2_0 = {
        "2(0).1": "0",
        "2(0).3": "0", 
        "2(0).4": tracking_number,
        "2(0).9": 24,  # 操作码
        "2(0).11": "0.0",
        "2(0).17": "0",
        "2(0).18": "0",
        "2(0).22": "1",
        "2(0).23": "1",
        "2(0).25": "z20",
        "2(0).27": "0",
        "2(0).29": "0",
        "2(0).31": "",
        "2(0).31.11": 89,
        "2(0).32": employee_id,
        "2(0).39": "0",
        "2(0).41": tracking_number,
        "2(0).42": "0",
        "2(0).43": "0",
        "2(0).44": tracking_number,
        "2(0).46": 24,  # 操作码
        "2(0).47": timestamp,
        "2(0).54": "XCRK",  # 应用类型
        "2(0).59": "0",
        "2(0).60": "0",
        "2(0).61": "0"
    }
    
    json_2_0 = json.dumps(packet_2_0, separators=(',', ':'))
    print(f"数据包大小: {len(json_2_0)} 字节")
    print(f"关键字段: 2(0).54=XCRK, 2(0).46=24")
    print(f"JSON示例: {json_2_0[:80]}...")
    
    # 测试2(1)类型数据包（建包/乡镇/到派操作）
    print("\n--- 2(1) 数据包结构 (建包/乡镇/到派操作) ---")
    packet_2_1 = {
        "2(1).1": "0",
        "2(1).3": "0",
        "2(1).4": tracking_number,
        "2(1).11": "0.0",
        "2(1).17": "0",
        "2(1).18": "0",
        "2(1).22": "1",
        "2(1).23": "1",
        "2(1).27": "0",
        "2(1).29": "0",
        "2(1).32": employee_id,
        "2(1).39": "0",
        "2(1).42": "0",
        "2(1).43": "0",
        "2(1).44": tracking_number,
        "2(1).46": 13,  # 操作码
        "2(1).47": timestamp,
        "2(1).54": "wddpyt",  # 应用类型
        "2(1).59": "0",
        "2(1).60": "0",
        "2(1).61": "0"
    }
    
    json_2_1 = json.dumps(packet_2_1, separators=(',', ':'))
    print(f"数据包大小: {len(json_2_1)} 字节")
    print(f"关键字段: 2(1).54=wddpyt, 2(1).46=13")
    print(f"JSON示例: {json_2_1[:80]}...")
    
    print("\n✓ 数据包结构验证完成")

def test_authentication_info():
    """测试认证信息"""
    print("\n" + "=" * 60)
    print("测试认证信息")
    print("=" * 60)
    
    # 从IDA分析中提取的认证信息
    auth_info = {
        "network_code": "530023",      # 网点编号
        "employee_id": "9528",         # 业务员工号
        "password": "881000",          # 业务员口令
        "center_code": "530001",       # 分拨中心代码
        "device_sn": "59992231000470", # 设备SN
        "version": "3.6.8.0821",      # 版本号
        "aes_key": "k301qsjbrh1s6ega" # AES密钥
    }
    
    print("认证参数:")
    for key, value in auth_info.items():
        print(f"  {key}: {value}")
    
    # 验证参数格式
    print("\n参数格式验证:")
    print(f"  网点编号长度: {len(auth_info['network_code'])} (应为6位)")
    print(f"  工号长度: {len(auth_info['employee_id'])} (应为4位)")
    print(f"  口令长度: {len(auth_info['password'])} (应为6位)")
    print(f"  设备SN长度: {len(auth_info['device_sn'])} (应为14位)")
    print(f"  AES密钥长度: {len(auth_info['aes_key'])} (应为16字节)")
    
    print("\n✓ 认证信息验证完成")

def test_login_data_format():
    """测试登录数据格式"""
    print("\n" + "=" * 60)
    print("测试登录数据格式")
    print("=" * 60)
    
    # 设备信息JSON
    device_info = {
        "imei": "59992231000470",
        "mac": "00:11:22:33:44:55",
        "serialNumber": "59992231000470"
    }
    device_json = json.dumps(device_info, separators=(',', ':'))
    
    # 登录数据JSON
    login_data = {
        "appType": "1",
        "company": "530023",
        "device": device_json,
        "digest": "",  # 稍后计算
        "password": "881000",
        "reqTime": "1640995200000",
        "uniqueCode": "test-unique-code",
        "user": "9528",
        "version": "3.6.8.0821"
    }
    
    # 计算摘要
    data_str = json.dumps(login_data, separators=(',', ':'))
    md5_hash = hashlib.md5()
    md5_hash.update(data_str.encode('utf-8'))
    digest = md5_hash.hexdigest()
    login_data["digest"] = digest
    
    print(f"设备信息JSON:")
    print(device_json)
    
    print(f"\n登录数据JSON:")
    print(json.dumps(login_data, indent=2, ensure_ascii=False))
    
    print(f"\n数据摘要: {digest}")
    print("\n✓ 登录数据格式验证完成")

def test_network_config():
    """测试网络配置"""
    print("\n" + "=" * 60)
    print("测试网络配置")
    print("=" * 60)
    
    # 从IDA分析中提取的网络配置
    network_config = {
        "base_url": "http://scan.yundasys.com:9900/rock",
        "login_endpoint": "/query/wdBqLogin/v1",
        "upload_endpoint": "/upload/outlet/scan/bq/v1",
        "user_agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
        "content_type": "application/octet-stream"
    }
    
    print("网络配置:")
    for key, value in network_config.items():
        print(f"  {key}: {value}")
    
    # 完整URL
    login_url = network_config["base_url"] + network_config["login_endpoint"]
    upload_url = network_config["base_url"] + network_config["upload_endpoint"]
    
    print(f"\n完整URL:")
    print(f"  登录URL: {login_url}")
    print(f"  上传URL: {upload_url}")
    
    print("\n✓ 网络配置验证完成")

def main():
    """主测试函数"""
    print("快递单号上传脚本 - IDA分析结果验证")
    print("基于伪代码分析的四种操作类型实现")
    
    # 执行各项测试
    test_operation_types()
    test_packet_structure()
    test_authentication_info()
    test_login_data_format()
    test_network_config()
    
    print("\n" + "=" * 60)
    print("所有验证测试完成")
    print("=" * 60)
    
    print("\n🎯 关键发现总结:")
    print("1. 发现四种不同的快递操作类型")
    print("2. 到达操作使用XCRK应用类型和操作码24")
    print("3. 建包/乡镇/到派使用wddpyt应用类型和操作码13")
    print("4. 数据包分为2(0)和2(1)两种类型")
    print("5. AES-ECB加密，密钥: k301qsjbrh1s6ega")
    print("6. 服务器: scan.yundasys.com:9900")
    
    print("\n📋 认证信息:")
    print("- 网点编号: 530023")
    print("- 工号: 9528") 
    print("- 口令: 881000")
    print("- 分拨中心: 530001")
    print("- 设备SN: 59992231000470")

if __name__ == "__main__":
    main()
