#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级AES解密脚本 - 尝试多种密钥变体和方法
"""

import base64
import json
import hashlib
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def generate_key_variants(base_key):
    """生成密钥的各种变体"""
    variants = []
    
    # 原始密钥
    variants.append(("原始密钥", base_key))
    
    # MD5哈希
    variants.append(("MD5哈希", hashlib.md5(base_key.encode()).hexdigest()[:16]))
    
    # SHA1哈希前16字节
    variants.append(("SHA1前16字节", hashlib.sha1(base_key.encode()).hexdigest()[:16]))
    
    # SHA256哈希前16字节
    variants.append(("SHA256前16字节", hashlib.sha256(base_key.encode()).hexdigest()[:16]))
    
    # 反转密钥
    variants.append(("反转密钥", base_key[::-1]))
    
    # 大写
    variants.append(("大写", base_key.upper()))
    
    # 小写
    variants.append(("小写", base_key.lower()))
    
    # 重复到32字节然后取前16字节
    extended = (base_key * 2)[:16]
    variants.append(("重复扩展", extended))
    
    # 添加常见后缀
    suffixes = ["0000", "1234", "abcd", "ABCD"]
    for suffix in suffixes:
        if len(base_key + suffix) >= 16:
            variants.append((f"添加{suffix}", (base_key + suffix)[:16]))
    
    return variants

def try_decrypt_with_key(encrypted_bytes, key_name, key):
    """尝试使用指定密钥解密"""
    try:
        key_bytes = key.encode('utf-8')[:16].ljust(16, b'\x00')
        
        # 尝试AES-ECB
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        decrypted = cipher.decrypt(encrypted_bytes)
        
        # 尝试去除填充
        try:
            unpadded = unpad(decrypted, AES.block_size)
            result = unpadded
        except:
            result = decrypted
        
        # 尝试UTF-8解码
        try:
            text = result.decode('utf-8')
            # 检查是否包含可打印字符
            printable_ratio = sum(1 for c in text if c.isprintable() or c in '\n\r\t') / len(text)
            if printable_ratio > 0.8:
                print(f"✓ {key_name} 解密成功!")
                print(f"  密钥: {key}")
                print(f"  结果长度: {len(text)}")
                print(f"  可打印字符比例: {printable_ratio:.2%}")
                print(f"  前100字符: {text[:100]}")
                
                # 尝试解析JSON
                try:
                    json_data = json.loads(text)
                    print(f"  ✓ 是有效的JSON!")
                    return text, json_data
                except:
                    print(f"  - 不是JSON格式")
                    return text, None
        except:
            pass
        
        # 检查是否包含常见字符串
        common_strings = [b'sessionId', b'token', b'digest', b'password', b'data', b'{', b'"']
        found_strings = [s for s in common_strings if s in result]
        if found_strings:
            print(f"⚠️  {key_name} 可能部分成功")
            print(f"  密钥: {key}")
            print(f"  发现字符串: {found_strings}")
            print(f"  十六进制前64字节: {result[:64].hex()}")
            
    except Exception as e:
        pass
    
    return None, None

def analyze_encrypted_data(encrypted_data):
    """分析加密数据"""
    print("=== 加密数据分析 ===")
    
    # Base64解码
    try:
        encrypted_bytes = base64.b64decode(encrypted_data)
        print(f"✓ Base64解码成功")
        print(f"  原始长度: {len(encrypted_data)}")
        print(f"  解码后长度: {len(encrypted_bytes)}")
        print(f"  是否为16的倍数: {len(encrypted_bytes) % 16 == 0}")
        print(f"  前32字节(hex): {encrypted_bytes[:32].hex()}")
        
        return encrypted_bytes
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")
        return None

def main():
    """主函数"""
    print("高级AES解密分析 - 乡镇驿站扫描数据")
    print("尝试多种密钥变体和解密方法")
    print("="*60)
    
    # 加密数据
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    # 分析加密数据
    encrypted_bytes = analyze_encrypted_data(encrypted_data)
    if not encrypted_bytes:
        return
    
    print(f"\n=== 尝试多种密钥变体 ===")
    
    # 基础密钥
    base_key = "k301qsjbrh1s6ega"
    
    # 生成密钥变体
    key_variants = generate_key_variants(base_key)
    
    # 添加一些其他可能的密钥
    additional_keys = [
        ("韵达相关", "yunda2023scan"),
        ("530001组合", "530001k301qsjb"),
        ("系统密钥", "yundasys123456"),
        ("扫描密钥", "scan2023k301qsj"),
        ("驿站密钥", "station530001k3"),
    ]
    key_variants.extend(additional_keys)
    
    success_count = 0
    results = []
    
    for key_name, key in key_variants:
        print(f"\n--- 尝试 {key_name}: {key} ---")
        text_result, json_result = try_decrypt_with_key(encrypted_bytes, key_name, key)
        
        if text_result:
            success_count += 1
            results.append((key_name, key, text_result, json_result))
            
            # 保存结果
            filename = f"decrypt_result_{key_name.replace(' ', '_')}.txt"
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"密钥类型: {key_name}\n")
                    f.write(f"密钥: {key}\n")
                    f.write(f"解密结果:\n{text_result}")
                print(f"  ✓ 结果已保存到 {filename}")
                
                if json_result:
                    json_filename = f"decrypt_result_{key_name.replace(' ', '_')}.json"
                    with open(json_filename, 'w', encoding='utf-8') as f:
                        json.dump(json_result, f, indent=2, ensure_ascii=False)
                    print(f"  ✓ JSON已保存到 {json_filename}")
            except Exception as e:
                print(f"  ❌ 保存失败: {e}")
    
    print(f"\n=== 解密结果总结 ===")
    print(f"尝试了 {len(key_variants)} 种密钥变体")
    print(f"成功解密: {success_count} 个")
    
    if success_count > 0:
        print(f"\n🎉 解密成功的密钥:")
        for key_name, key, text_result, json_result in results:
            print(f"  - {key_name}: {key}")
            if json_result:
                print(f"    包含JSON字段: {list(json_result.keys())}")
    else:
        print(f"\n❌ 所有密钥尝试都失败了")
        print(f"可能的原因:")
        print(f"1. 密钥不正确或需要进一步处理")
        print(f"2. 使用了CBC模式而不是ECB模式")
        print(f"3. 有额外的加密层或编码")
        print(f"4. 数据损坏或不完整")

if __name__ == "__main__":
    main()
