#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AES解密脚本 - 使用从伪代码中发现的密钥
"""

import base64
import json

def simple_aes_ecb_decrypt(encrypted_data: bytes, key: str) -> bytes:
    """
    简单的AES ECB解密实现（不依赖外部库）
    注意：这是一个简化版本，仅用于演示
    """
    # 这里我们先尝试XOR解密作为替代
    key_bytes = key.encode('utf-8')
    if len(key_bytes) < 16:
        # 填充密钥到16字节
        key_bytes = (key_bytes * (16 // len(key_bytes) + 1))[:16]
    elif len(key_bytes) > 16:
        # 截断密钥到16字节
        key_bytes = key_bytes[:16]
    
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        decrypted.append(byte ^ key_bytes[i % 16])
    
    return bytes(decrypted)

def try_decrypt_with_key(encrypted_data: bytes, key: str):
    """尝试使用指定密钥解密"""
    print(f"\n=== 尝试密钥: {key} ===")
    print(f"密钥长度: {len(key)}")
    
    try:
        # 方法1: 简单XOR解密
        decrypted = simple_aes_ecb_decrypt(encrypted_data, key)
        
        # 尝试UTF-8解码
        try:
            result = decrypted.decode('utf-8')
            print(f"✓ UTF-8解码成功!")
            print(f"解密结果: {result}")
            return result
        except UnicodeDecodeError:
            print("UTF-8解码失败，尝试其他编码...")
            
            # 尝试其他编码
            for encoding in ['latin-1', 'cp1252', 'gbk']:
                try:
                    result = decrypted.decode(encoding)
                    if len(result) > 10 and any(c.isprintable() for c in result):
                        print(f"✓ {encoding}解码成功!")
                        print(f"解密结果: {result[:200]}...")
                        return result
                except:
                    continue
            
            # 显示十六进制
            print(f"解密结果(hex): {decrypted[:64].hex()}...")
            
            # 检查是否包含JSON结构
            hex_str = decrypted.hex()
            if '7b' in hex_str and '7d' in hex_str:  # { 和 }
                print("⚠️  可能包含JSON结构")
            
            return decrypted
            
    except Exception as e:
        print(f"解密失败: {e}")
        return None

def analyze_decrypted_data(data):
    """分析解密后的数据"""
    if isinstance(data, str):
        print(f"\n=== 解密数据分析 ===")
        print(f"数据类型: 字符串")
        print(f"数据长度: {len(data)}")
        
        # 检查是否是JSON
        try:
            json_data = json.loads(data)
            print("✓ 数据是有效的JSON!")
            print("JSON内容:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
            return json_data
        except:
            print("数据不是JSON格式")
            
        # 检查是否包含关键字段
        keywords = ['sessionId', 'token', 'digest', 'password', 'reqTime', '530001']
        found_keywords = [kw for kw in keywords if kw in data]
        if found_keywords:
            print(f"发现关键字段: {found_keywords}")
            
    elif isinstance(data, bytes):
        print(f"\n=== 解密数据分析 ===")
        print(f"数据类型: 字节")
        print(f"数据长度: {len(data)}")
        print(f"前64字节(hex): {data[:64].hex()}")
        
        # 检查是否包含可打印字符
        printable_count = sum(1 for b in data[:100] if 32 <= b <= 126)
        print(f"可打印字符比例: {printable_count/min(100, len(data))*100:.1f}%")

def main():
    """主函数"""
    # 乡镇驿站扫描的加密数据
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    print("乡镇驿站扫描数据AES解密分析")
    print("=" * 60)
    
    # Base64解码
    try:
        decoded_data = base64.b64decode(encrypted_data)
        print(f"Base64解码成功，数据长度: {len(decoded_data)}")
        print(f"前32字节(hex): {decoded_data[:32].hex()}")
    except Exception as e:
        print(f"Base64解码失败: {e}")
        return
    
    # 从伪代码中发现的真实密钥
    real_key = "k301qsjbrh1s6ega"
    
    print(f"\n🔑 使用从伪代码中发现的真实密钥: {real_key}")
    
    # 尝试解密
    result = try_decrypt_with_key(decoded_data, real_key)
    
    if result:
        analyze_decrypted_data(result)
        
        # 如果解密成功，保存结果
        if isinstance(result, str):
            try:
                with open('decrypted_result.txt', 'w', encoding='utf-8') as f:
                    f.write(result)
                print(f"\n✓ 解密结果已保存到 decrypted_result.txt")
            except Exception as e:
                print(f"保存文件失败: {e}")
    else:
        print("\n❌ 解密失败")
        print("可能的原因:")
        print("1. 密钥不正确")
        print("2. 加密算法不是简单的XOR")
        print("3. 需要使用真正的AES库")
        print("4. 数据可能有额外的编码层")
        
        print(f"\n建议:")
        print(f"1. 安装pycryptodome: pip install pycryptodome")
        print(f"2. 使用真正的AES-ECB解密")
        print(f"3. 检查是否需要IV或其他参数")

if __name__ == "__main__":
    main()
