#!/usr/bin/env python3
"""
ADB Fiddler Proxy Tool - 用于配置安卓设备通过Fiddler进行抓包的工具
使用ADB连接安卓设备并自动配置代理设置

特点:
- 支持root和非root设备
- 自动检测设备并配置代理
- 提供证书安装指南
- 支持端口转发功能
- 交互式菜单和命令行模式
"""

import os
import re
import sys
import socket
import subprocess
import argparse
import time
from typing import List, Tuple, Optional, Dict

class AdbFiddlerProxy:
    def __init__(self, fiddler_host: str = None, fiddler_port: int = 8888):
        """初始化ADB Fiddler代理工具"""
        # 如果未指定Fiddler主机，尝试获取本机IP
        self.fiddler_host = fiddler_host or self._get_local_ip()
        self.fiddler_port = fiddler_port
        self.adb_path = "adb"  # 假设adb在PATH中
        
    def _get_local_ip(self) -> str:
        """获取本机IP地址，用于Fiddler代理设置"""
        try:
            # 创建一个临时socket连接来确定本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except Exception:
            print("警告: 无法自动检测本机IP，使用127.0.0.1")
            return "127.0.0.1"
    
    def _run_command(self, command: List[str]) -> Tuple[int, str, str]:
        """运行命令并返回退出码、标准输出和标准错误"""
        try:
            process = subprocess.Popen(
                command, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            return process.returncode, stdout, stderr
        except Exception as e:
            return -1, "", str(e)
    
    def check_adb(self) -> bool:
        """检查ADB是否可用"""
        code, stdout, stderr = self._run_command([self.adb_path, "version"])
        if code != 0:
            print(f"错误: ADB不可用。请确保已安装ADB并添加到PATH中。")
            print(f"错误详情: {stderr}")
            return False
        print(f"ADB可用: {stdout.splitlines()[0]}")
        return True
    
    def get_connected_devices(self) -> List[str]:
        """获取已连接的安卓设备列表"""
        devices = []
        code, stdout, stderr = self._run_command([self.adb_path, "devices"])
        if code != 0:
            print(f"错误: 无法获取设备列表。{stderr}")
            return devices
        
        # 解析设备列表输出
        lines = stdout.strip().splitlines()
        if len(lines) <= 1:
            print("没有发现已连接的设备")
            return devices
        
        for line in lines[1:]:  # 跳过第一行 "List of devices attached"
            if line.strip():
                parts = line.split()
                if len(parts) >= 2 and parts[1] == "device":
                    devices.append(parts[0])
        
        return devices
    
    def select_device(self, devices: List[str]) -> Optional[str]:
        """如果有多个设备，让用户选择一个"""
        if not devices:
            return None
        if len(devices) == 1:
            print(f"使用设备: {devices[0]}")
            return devices[0]
        
        print("发现多个设备:")
        for i, device in enumerate(devices):
            print(f"{i+1}. {device}")
        
        while True:
            try:
                choice = int(input("请选择设备 (输入编号): "))
                if 1 <= choice <= len(devices):
                    return devices[choice-1]
                print(f"无效选择，请输入1到{len(devices)}之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def get_device_info(self, device_id: str) -> Dict[str, str]:
        """获取设备信息"""
        info = {}
        
        # 获取Android版本
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "shell", "getprop", "ro.build.version.release"
        ])
        if code == 0:
            info["android_version"] = stdout.strip()
        
        # 获取设备型号
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "shell", "getprop", "ro.product.model"
        ])
        if code == 0:
            info["model"] = stdout.strip()
        
        return info
    
    def set_proxy(self, device_id: str) -> bool:
        """设置设备使用Fiddler代理"""
        print(f"正在为设备 {device_id} 设置Fiddler代理 ({self.fiddler_host}:{self.fiddler_port})...")
        
        # 检查设备是否已root
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "shell", "whoami"
        ])
        is_rooted = code == 0 and "root" in stdout.strip()
        
        if is_rooted:
            # 如果设备已root，可以直接修改全局设置
            print("检测到root权限，将使用root方式设置全局代理")
            return self._set_proxy_for_rooted_device(device_id)
        else:
            # 否则使用settings命令设置代理
            print("设备未root，将尝试使用普通方式设置代理")
            print("注意：非root设备仍然可以抓包，但可能需要手动配置某些设置")
            return self._set_proxy_for_non_rooted_device(device_id)
    
    def _set_proxy_for_rooted_device(self, device_id: str) -> bool:
        """为已root的设备设置全局代理"""
        commands = [
            f"settings put global http_proxy {self.fiddler_host}:{self.fiddler_port}"
        ]
        
        for cmd in commands:
            code, stdout, stderr = self._run_command([
                self.adb_path, "-s", device_id, "shell", cmd
            ])
            if code != 0:
                print(f"错误: 无法设置代理。{stderr}")
                return False
        
        print("代理设置成功 (root方式)")
        return True
    
    def _set_proxy_for_non_rooted_device(self, device_id: str) -> bool:
        """为非root设备设置代理"""
        # 对于Android 8.0及以上版本
        commands = [
            f"settings put global http_proxy {self.fiddler_host}:{self.fiddler_port}"
        ]
        
        success = False
        for cmd in commands:
            code, stdout, stderr = self._run_command([
                self.adb_path, "-s", device_id, "shell", cmd
            ])
            if code == 0:
                success = True
                break
        
        if success:
            print("代理设置成功")
            return True
        else:
            print("警告: 无法通过settings命令设置代理")
            print("请尝试手动设置: 设置 -> Wi-Fi -> 长按当前网络 -> 修改网络 -> 高级选项 -> 代理")
            print(f"手动设置代理主机: {self.fiddler_host}, 端口: {self.fiddler_port}")
            return False
    
    def clear_proxy(self, device_id: str) -> bool:
        """清除设备上的代理设置"""
        print(f"正在为设备 {device_id} 清除代理设置...")
        
        # 尝试多种方式清除代理
        commands = [
            "settings put global http_proxy :0",  # 较新的Android版本
            "settings delete global http_proxy",  # 尝试删除设置
            "settings put global http_proxy \"\"" # 尝试设置为空
        ]
        
        for cmd in commands:
            code, stdout, stderr = self._run_command([
                self.adb_path, "-s", device_id, "shell", cmd
            ])
            if code == 0:
                print("代理已清除")
                return True
        
        print("警告: 无法自动清除代理，请手动清除")
        print("请进入: 设置 -> Wi-Fi -> 长按当前网络 -> 修改网络 -> 高级选项 -> 代理 -> 无")
        return False
    
    def check_proxy_status(self, device_id: str) -> bool:
        """检查设备当前的代理设置"""
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "shell", "settings get global http_proxy"
        ])
        
        if code != 0:
            print(f"错误: 无法获取代理状态。{stderr}")
            return False
        
        proxy_setting = stdout.strip()
        if not proxy_setting or proxy_setting == ":0" or proxy_setting == "null":
            print("当前没有设置代理")
            return False
        
        print(f"当前代理设置: {proxy_setting}")
        return True
    
    def install_fiddler_cert(self, device_id: str) -> bool:
        """
        指导用户如何在设备上安装Fiddler证书
        注意: 自动安装证书需要root权限，这里提供手动指导
        """
        print("\n=== Fiddler证书安装指南 ===")
        print("注意: 无论设备是否root，都可以安装证书并抓包，但步骤和覆盖范围可能不同")
        print(f"1. 确保Fiddler在电脑上运行，并在Tools > Options > HTTPS中启用了HTTPS捕获")
        print(f"2. 在安卓设备上打开浏览器，访问: http://{self.fiddler_host}:{self.fiddler_port}")
        print(f"3. 点击页面上的'FiddlerRoot certificate'链接下载证书")
        print(f"4. 在设备上安装下载的证书:")
        print(f"   - 对于Android 7及以下: 直接从下载文件安装")
        print(f"   - 对于Android 8及以上: 进入设置 > 安全 > 加密与凭据 > 安装证书 > CA证书")
        print(f"5. 对于Android 7+的HTTPS流量:")
        print(f"   - 非root设备: 只能抓取支持用户证书的应用流量")
        print(f"   - 已root设备: 可以通过额外步骤抓取更多应用的流量")
        print(f"   - 自己开发的应用: 可以在网络安全配置中添加信任用户证书\n")
        
        return True
    
    def port_forward(self, device_id: str, local_port: int, remote_port: int) -> bool:
        """设置端口转发，用于特殊情况"""
        print(f"设置端口转发: 本地{local_port} -> 设备{remote_port}")
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "forward", 
            f"tcp:{local_port}", f"tcp:{remote_port}"
        ])
        
        if code != 0:
            print(f"错误: 无法设置端口转发。{stderr}")
            return False
        
        print(f"端口转发设置成功: {local_port} -> {remote_port}")
        return True
    
    def list_port_forwards(self, device_id: str) -> bool:
        """列出当前的端口转发设置"""
        code, stdout, stderr = self._run_command([
            self.adb_path, "forward", "--list"
        ])
        
        if code != 0:
            print(f"错误: 无法获取端口转发列表。{stderr}")
            return False
        
        if stdout.strip():
            print("当前端口转发:")
            print(stdout)
        else:
            print("当前没有端口转发设置")
        
        return True
    
    def remove_port_forward(self, local_port: int) -> bool:
        """移除特定的端口转发"""
        code, stdout, stderr = self._run_command([
            self.adb_path, "forward", "--remove", f"tcp:{local_port}"
        ])
        
        if code != 0:
            print(f"错误: 无法移除端口转发。{stderr}")
            return False
        
        print(f"已移除端口转发: {local_port}")
        return True
    
    def test_connection(self, device_id: str) -> bool:
        """测试设备是否可以通过代理连接到互联网"""
        print("测试通过代理的网络连接...")
        test_url = "http://www.google.com"
        
        code, stdout, stderr = self._run_command([
            self.adb_path, "-s", device_id, "shell", 
            f"curl -v -x {self.fiddler_host}:{self.fiddler_port} {test_url}"
        ])
        
        if code != 0:
            print(f"警告: 连接测试失败。设备可能没有curl，或者代理设置有问题。")
            print(f"请在Fiddler中检查是否有来自设备的请求。")
            return False
        
        if "200 OK" in stdout or "302 Found" in stdout:
            print("连接测试成功！设备可以通过Fiddler代理访问网络。")
            return True
        else:
            print(f"警告: 连接测试结果不明确。请在Fiddler中检查是否有来自设备的请求。")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ADB Fiddler代理工具 - 配置安卓设备使用Fiddler进行抓包 (支持root和非root设备)")
    parser.add_argument("--host", help="Fiddler代理主机IP (默认: 自动检测)")
    parser.add_argument("--port", type=int, default=8888, help="Fiddler代理端口 (默认: 8888)")
    
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 设置代理命令
    set_parser = subparsers.add_parser("set", help="设置Fiddler代理")
    set_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    # 清除代理命令
    clear_parser = subparsers.add_parser("clear", help="清除代理设置")
    clear_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    # 检查代理状态命令
    status_parser = subparsers.add_parser("status", help="检查代理状态")
    status_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    # 安装证书指南命令
    cert_parser = subparsers.add_parser("cert", help="显示Fiddler证书安装指南")
    cert_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    # 端口转发命令
    forward_parser = subparsers.add_parser("forward", help="设置端口转发")
    forward_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    forward_parser.add_argument("--local", type=int, required=True, help="本地端口")
    forward_parser.add_argument("--remote", type=int, required=True, help="设备端口")
    
    # 列出端口转发命令
    list_forward_parser = subparsers.add_parser("list-forwards", help="列出端口转发")
    list_forward_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    # 移除端口转发命令
    remove_forward_parser = subparsers.add_parser("remove-forward", help="移除端口转发")
    remove_forward_parser.add_argument("--local", type=int, required=True, help="要移除的本地端口")
    
    # 测试连接命令
    test_parser = subparsers.add_parser("test", help="测试通过代理的连接")
    test_parser.add_argument("--device", help="指定设备ID (如果有多个设备)")
    
    args = parser.parse_args()
    
    # 创建工具实例
    proxy_tool = AdbFiddlerProxy(args.host, args.port)
    
    # 检查ADB是否可用
    if not proxy_tool.check_adb():
        return 1
    
    # 获取已连接的设备
    devices = proxy_tool.get_connected_devices()
    if not devices:
        print("错误: 没有找到已连接的安卓设备")
        return 1
    
    # 选择设备
    device_id = args.device if hasattr(args, 'device') and args.device else proxy_tool.select_device(devices)
    if not device_id:
        print("错误: 未选择设备")
        return 1
    
    # 获取并显示设备信息
    device_info = proxy_tool.get_device_info(device_id)
    if device_info:
        print(f"设备信息: {device_info.get('model', '未知型号')} (Android {device_info.get('android_version', '未知版本')})")
    
    # 根据命令执行相应操作
    if args.command == "set":
        proxy_tool.set_proxy(device_id)
    elif args.command == "clear":
        proxy_tool.clear_proxy(device_id)
    elif args.command == "status":
        proxy_tool.check_proxy_status(device_id)
    elif args.command == "cert":
        proxy_tool.install_fiddler_cert(device_id)
    elif args.command == "forward":
        proxy_tool.port_forward(device_id, args.local, args.remote)
    elif args.command == "list-forwards":
        proxy_tool.list_port_forwards(device_id)
    elif args.command == "remove-forward":
        proxy_tool.remove_port_forward(args.local)
    elif args.command == "test":
        proxy_tool.test_connection(device_id)
    else:
        # 如果没有指定命令，显示交互式菜单
        while True:
            print("\n=== ADB Fiddler代理工具 (支持root和非root设备) ===")
            print(f"当前设备: {device_id}")
            print(f"Fiddler代理: {proxy_tool.fiddler_host}:{proxy_tool.fiddler_port}")
            print("1. 设置Fiddler代理")
            print("2. 清除代理设置")
            print("3. 检查代理状态")
            print("4. 显示Fiddler证书安装指南")
            print("5. 设置端口转发")
            print("6. 列出端口转发")
            print("7. 移除端口转发")
            print("8. 测试连接")
            print("9. 切换设备")
            print("0. 退出")
            
            choice = input("请选择操作: ")
            
            if choice == "1":
                proxy_tool.set_proxy(device_id)
            elif choice == "2":
                proxy_tool.clear_proxy(device_id)
            elif choice == "3":
                proxy_tool.check_proxy_status(device_id)
            elif choice == "4":
                proxy_tool.install_fiddler_cert(device_id)
            elif choice == "5":
                try:
                    local_port = int(input("本地端口: "))
                    remote_port = int(input("设备端口: "))
                    proxy_tool.port_forward(device_id, local_port, remote_port)
                except ValueError:
                    print("错误: 请输入有效的端口号")
            elif choice == "6":
                proxy_tool.list_port_forwards(device_id)
            elif choice == "7":
                try:
                    local_port = int(input("要移除的本地端口: "))
                    proxy_tool.remove_port_forward(local_port)
                except ValueError:
                    print("错误: 请输入有效的端口号")
            elif choice == "8":
                proxy_tool.test_connection(device_id)
            elif choice == "9":
                new_device = proxy_tool.select_device(devices)
                if new_device:
                    device_id = new_device
                    device_info = proxy_tool.get_device_info(device_id)
                    if device_info:
                        print(f"设备信息: {device_info.get('model', '未知型号')} (Android {device_info.get('android_version', '未知版本')})")
            elif choice == "0":
                break
            else:
                print("无效选择，请重试")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
