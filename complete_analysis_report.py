#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的IDA分析报告 - 基于伪代码发现的认证信息和网络请求
"""

import json
from datetime import datetime

def generate_complete_report():
    """生成完整的分析报告"""
    
    report = {
        "analysis_date": datetime.now().isoformat(),
        "title": "韵达物流系统完整分析报告",
        "summary": "基于IDA伪代码分析的认证信息、网络请求和业务逻辑",
        
        # 认证信息
        "authentication": {
            "login_endpoint": "http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1",
            "login_payload": {
                "appType": "1",
                "company": "[company]",
                "device": "[device]", 
                "digest": "[digest]",
                "password": "[password]",
                "reqTime": "[reqTime]",
                "uniqueCode": "[token]",
                "user": "[user]",
                "version": "3.6.8.0821"
            },
            "headers": {
                "token": "[token]",
                "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
                "Content-Type": "application/octet-stream"
            },
            "response_fields": {
                "sessionId": "从响应中提取的会话ID"
            }
        },
        
        # 四个菜单的网络请求
        "menu_endpoints": {
            "1_delivery_arrival": {
                "name": "派件到达",
                "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1",
                "method": "POST",
                "business_rule": "固定设置分拨中心代码 530001",
                "headers": {
                    "token": "[token]",
                    "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
                    "Content-Type": "application/octet-stream"
                }
            },
            "2_dispatch_integration": {
                "name": "到派一体", 
                "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1",
                "method": "POST",
                "business_rule": "单号输入时长度等于4的代码验证",
                "headers": {
                    "token": "[token]",
                    "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
                    "Content-Type": "application/octet-stream"
                }
            },
            "3_rural_station_scan": {
                "name": "乡镇驿站扫描",
                "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", 
                "method": "POST",
                "business_rule": "单号输入时长度等于4的代码验证",
                "headers": {
                    "token": "[token]",
                    "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
                    "Content-Type": "application/octet-stream"
                }
            },
            "4_package_collection": {
                "name": "集包扫描",
                "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1",
                "method": "POST", 
                "business_rule": "单号输入时开头为9的代码 + 下一站为预设6位数代码",
                "headers": {
                    "token": "[token]",
                    "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
                    "Content-Type": "application/octet-stream"
                }
            }
        },
        
        # 加密信息
        "encryption": {
            "algorithm": "AES-ECB",
            "key": "k301qsjbrh1s6ega",
            "key_length": 16,
            "encoding": "Base64",
            "process": "原始数据 → AES-ECB加密 → Base64编码",
            "pseudocode_locations": {
                "key_definition": "第9756行",
                "algorithm_specification": "第9758行", 
                "encryption_function": "第9759行 sub_404606",
                "base64_function": "第9768行 sub_406AF8"
            }
        },
        
        # 业务逻辑规则
        "business_rules": {
            "delivery_arrival": {
                "rule": "派件到达时固定设置分拨中心代码为530001",
                "validation": "distribution_center_code == '530001'"
            },
            "dispatch_integration": {
                "rule": "到派一体操作时验证输入代码长度为4位数字",
                "validation": "len(input_code) == 4 and input_code.isdigit()"
            },
            "rural_station_scan": {
                "rule": "乡镇驿站扫描时验证输入代码长度为4位数字", 
                "validation": "len(input_code) == 4 and input_code.isdigit()"
            },
            "package_collection": {
                "rule": "集包扫描时验证单号以9开头且下一站代码为6位数字",
                "validation": "tracking_number.startswith('9') and len(next_station) == 6 and next_station.isdigit()"
            }
        },
        
        # 测试数据
        "test_data": {
            "real_tracking_numbers": [
                "312799099115886",
                "434640497727147"
            ],
            "analysis": "这些15位快递单号不符合集包扫描的'以9开头'规则，说明集包扫描针对的是特定的内部代码格式",
            "sample_encrypted_data": "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQ...",
            "data_source": "乡镇驿站扫描抓包数据"
        },
        
        # 技术细节
        "technical_details": {
            "server_info": {
                "domain": "scan.yundasys.com",
                "port": 9900,
                "protocol": "HTTP"
            },
            "api_structure": {
                "base_path": "/rock",
                "login_path": "/rock/query/wdBqLogin/v1",
                "scan_path": "/rock/upload/outlet/scan/bq/v1"
            },
            "version_info": {
                "app_version": "3.6.8.0821",
                "app_type": "1"
            }
        },
        
        # 安全分析
        "security_analysis": {
            "encryption_strength": "AES-128-ECB (相对较弱，ECB模式不推荐)",
            "authentication_method": "基于token和digest的认证",
            "potential_vulnerabilities": [
                "ECB模式可能泄露数据模式",
                "HTTP协议未加密传输",
                "固定的User-Agent可能被检测"
            ],
            "recommendations": [
                "升级到AES-CBC或AES-GCM模式",
                "使用HTTPS协议",
                "实现更强的身份验证机制"
            ]
        }
    }
    
    return report

def save_report():
    """保存分析报告"""
    report = generate_complete_report()
    
    # 保存JSON格式
    with open('complete_analysis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 保存可读格式
    with open('complete_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write("韵达物流系统完整分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("📋 认证信息\n")
        f.write("-" * 20 + "\n")
        f.write(f"登录端点: {report['authentication']['login_endpoint']}\n")
        f.write("登录载荷:\n")
        for key, value in report['authentication']['login_payload'].items():
            f.write(f"  {key}: {value}\n")
        f.write("\n")
        
        f.write("🌐 四个菜单的网络请求\n")
        f.write("-" * 30 + "\n")
        for menu_id, menu_info in report['menu_endpoints'].items():
            f.write(f"{menu_info['name']}:\n")
            f.write(f"  端点: {menu_info['endpoint']}\n")
            f.write(f"  业务规则: {menu_info['business_rule']}\n")
            f.write("\n")
        
        f.write("🔐 加密信息\n")
        f.write("-" * 15 + "\n")
        f.write(f"算法: {report['encryption']['algorithm']}\n")
        f.write(f"密钥: {report['encryption']['key']}\n")
        f.write(f"流程: {report['encryption']['process']}\n")
        f.write("\n")
        
        f.write("📊 业务逻辑规则\n")
        f.write("-" * 20 + "\n")
        for rule_name, rule_info in report['business_rules'].items():
            f.write(f"{rule_info['rule']}\n")
            f.write(f"  验证: {rule_info['validation']}\n")
            f.write("\n")
        
        f.write("🔍 安全分析\n")
        f.write("-" * 15 + "\n")
        f.write(f"加密强度: {report['security_analysis']['encryption_strength']}\n")
        f.write("潜在漏洞:\n")
        for vuln in report['security_analysis']['potential_vulnerabilities']:
            f.write(f"  - {vuln}\n")
        f.write("建议:\n")
        for rec in report['security_analysis']['recommendations']:
            f.write(f"  - {rec}\n")
    
    print("✅ 完整分析报告已生成:")
    print("  - complete_analysis_report.json (JSON格式)")
    print("  - complete_analysis_report.txt (可读格式)")

def print_summary():
    """打印分析摘要"""
    print("🎯 韵达物流系统分析摘要")
    print("=" * 40)
    print()
    
    print("✅ 已确认发现:")
    print("1. 认证端点: http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1")
    print("2. 四个菜单共用端点: http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1")
    print("3. AES加密密钥: k301qsjbrh1s6ega")
    print("4. 完整的业务逻辑规则")
    print("5. 认证信息格式和字段")
    print()
    
    print("📋 四个菜单:")
    print("1. 派件到达 - 固定分拨中心代码530001")
    print("2. 到派一体 - 4位数字代码验证")
    print("3. 乡镇驿站扫描 - 4位数字代码验证")
    print("4. 集包扫描 - 9开头单号+6位站点代码")
    print()
    
    print("🔐 加密方案:")
    print("- 算法: AES-ECB")
    print("- 密钥: k301qsjbrh1s6ega (16字节)")
    print("- 编码: Base64")
    print("- 流程: 数据 → AES加密 → Base64编码")

if __name__ == "__main__":
    print_summary()
    save_report()
    print("\n🎉 分析完成！所有信息已从伪代码中成功提取。")
