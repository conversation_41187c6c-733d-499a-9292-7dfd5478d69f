import time
import logging
import pywinauto
from pywinauto.timings import Timings

logger = logging.getLogger(__name__)

# 增加超时时间，提高稳定性
Timings.window_find_timeout = 10
Timings.app_connect_timeout = 10
Timings.app_start_timeout = 10

class UIHelper:
    """UI操作辅助类，提供更健壮的UI元素定位和操作方法"""
    
    def __init__(self):
        """初始化UI辅助类"""
        self.retry_count = 3
        self.retry_delay = 2
    
    def connect_app(self, title_pattern, backend="uia"):
        """连接到应用，支持重试"""
        for attempt in range(self.retry_count):
            try:
                app = pywinauto.Application(backend=backend).connect(title_re=title_pattern)
                window = app.window(title_re=title_pattern)
                return app, window
            except Exception as e:
                logger.warning(f"连接应用失败 (尝试 {attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"连接应用失败，已达最大重试次数: {e}")
                    raise
    
    def find_element(self, parent, **kwargs):
        """查找UI元素，支持重试和多种定位方式"""
        for attempt in range(self.retry_count):
            try:
                # 尝试多种定位方式
                try:
                    # 首先尝试使用提供的参数定位
                    element = parent.child_window(**kwargs)
                    element.wait('visible', timeout=5)
                    return element
                except Exception:
                    # 如果提供了title，尝试使用模糊匹配
                    if 'title' in kwargs and not kwargs.get('title_re'):
                        title = kwargs.pop('title')
                        element = parent.child_window(title_re=f".*{title}.*", **kwargs)
                        element.wait('visible', timeout=5)
                        return element
                    raise
            except Exception as e:
                logger.warning(f"查找元素失败 (尝试 {attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"查找元素失败，已达最大重试次数: {e}")
                    raise
    
    def click_element(self, element):
        """点击元素，支持重试"""
        for attempt in range(self.retry_count):
            try:
                element.set_focus()
                element.click_input()
                return True
            except Exception as e:
                logger.warning(f"点击元素失败 (尝试 {attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"点击元素失败，已达最大重试次数: {e}")
                    raise
    
    def type_text(self, element, text):
        """输入文本，支持重试"""
        for attempt in range(self.retry_count):
            try:
                element.set_focus()
                element.type_keys("^a{BACKSPACE}")  # 清空输入框
                element.type_keys(text, with_spaces=True)
                return True
            except Exception as e:
                logger.warning(f"输入文本失败 (尝试 {attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"输入文本失败，已达最大重试次数: {e}")
                    raise
    
    def send_message(self, window, group_name, message, app_type):
        """发送消息到指定群聊"""
        try:
            # 激活窗口
            window.set_focus()
            
            # 找到并点击目标群聊
            chat_list = self.find_element(window, control_type="List")
            target_group = self.find_element(chat_list, title=group_name, control_type="ListItem")
            self.click_element(target_group)
            time.sleep(1)
            
            # 找到输入框
            input_box = self.find_element(window, control_type="Edit")
            
            # 输入消息并发送
            self.type_text(input_box, message)
            input_box.type_keys("{ENTER}")
            
            logger.info(f"已发送消息到{app_type}群: {message}")
            return True
        except Exception as e:
            logger.error(f"发送{app_type}消息失败: {e}")
            return False
    
    def get_messages(self, window, group_name, app_type, processed_messages):
        """获取指定群聊的最新消息"""
        try:
            # 激活窗口
            window.set_focus()
            
            # 找到并点击目标群聊
            chat_list = self.find_element(window, control_type="List")
            target_group = self.find_element(chat_list, title=group_name, control_type="ListItem")
            self.click_element(target_group)
            time.sleep(1)
            
            # 根据应用类型获取消息
            if app_type == "dingtalk":
                return self._get_dingtalk_messages(window, processed_messages)
            elif app_type == "wechat":
                return self._get_wechat_messages(window, processed_messages)
            else:
                logger.error(f"不支持的应用类型: {app_type}")
                return []
        except Exception as e:
            logger.error(f"获取{app_type}消息失败: {e}")
            return []
    
    def _get_dingtalk_messages(self, window, processed_messages):
        """获取钉钉群的最新消息"""
        try:
            # 获取消息区域
            message_area = self.find_element(
                window, 
                control_type="Custom", 
                class_name="conversation-message-list"
            )
            
            # 获取所有消息元素
            message_elements = message_area.children(control_type="Text")
            
            # 提取消息内容和发送者
            messages = []
            for i in range(len(message_elements) - 1, -1, -1):
                try:
                    # 尝试获取消息内容
                    message_text = message_elements[i].window_text()
                    
                    # 尝试获取发送者（可能在相邻元素中）
                    sender = "未知用户"
                    if i > 0:
                        sender_element = message_elements[i-1]
                        if ":" not in sender_element.window_text():
                            sender = sender_element.window_text()
                    
                    # 创建消息标识符（发送者+消息内容）
                    message_id = f"{sender}:{message_text}"
                    
                    # 如果是新消息，添加到结果中
                    if message_id not in processed_messages:
                        messages.append({
                            "sender": sender,
                            "content": message_text,
                            "id": message_id
                        })
                        processed_messages.add(message_id)
                except Exception as e:
                    logger.warning(f"处理钉钉消息元素时出错: {e}")
            
            return messages
        except Exception as e:
            logger.error(f"获取钉钉消息失败: {e}")
            return []
    
    def _get_wechat_messages(self, window, processed_messages):
        """获取微信群的最新消息"""
        try:
            # 获取消息区域
            message_area = self.find_element(window, control_type="List", class_name="MessageList")
            
            # 获取所有消息元素
            message_elements = message_area.children(control_type="ListItem")
            
            # 提取消息内容和发送者
            messages = []
            for element in message_elements:
                try:
                    # 尝试获取发送者和消息内容
                    sender_element = element.child_window(control_type="Text", found_index=0)
                    content_element = element.child_window(control_type="Text", found_index=1)
                    
                    sender = sender_element.window_text()
                    content = content_element.window_text()
                    
                    # 创建消息标识符
                    message_id = f"{sender}:{content}"
                    
                    # 如果是新消息，添加到结果中
                    if message_id not in processed_messages:
                        messages.append({
                            "sender": sender,
                            "content": content,
                            "id": message_id
                        })
                        processed_messages.add(message_id)
                except Exception as e:
                    logger.warning(f"处理微信消息元素时出错: {e}")
            
            return messages
        except Exception as e:
            logger.error(f"获取微信消息失败: {e}")
            return []
