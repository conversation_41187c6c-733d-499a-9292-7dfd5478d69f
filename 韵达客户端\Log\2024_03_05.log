﻿#Software: 韵达自动化扫描客户端
#ProcessID: 11564 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,223M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:31:21.688  7 Y P KuangScannerNet 准备开始监听4个服务器
16:31:21.701  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:31:21.702  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:31:21.711  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:31:21.712  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:31:21.713  7 Y P KuangScannerNet 准备就绪！
16:31:21.730  7 Y P Http 准备开始监听2个服务器
16:31:21.732  7 Y P Http 开始监听 tcp://0.0.0.0:8080
16:31:21.732  7 Y P Http 开始监听 tcp://[::]:8080
16:31:21.733  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 14632 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,257M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:33:03.375 14 Y P KuangScannerNet 准备开始监听4个服务器
16:33:03.473 14 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:33:03.475 14 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:33:03.477 14 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:33:03.496 14 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:33:03.496 14 Y P KuangScannerNet 准备就绪！
16:33:03.509 14 Y P Http 准备开始监听2个服务器
16:33:03.522 14 Y P Http 开始监听 tcp://0.0.0.0:8080
16:33:03.542 14 Y P Http 开始监听 tcp://[::]:8080
16:33:03.542 14 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 15980 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,245M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32763/1000
#Date: 2024-03-05
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:35:05.281 17 Y P KuangScannerNet 准备开始监听4个服务器
16:35:05.395 17 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:35:05.432 17 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:35:05.435 17 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:35:05.435 17 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:35:05.435 17 Y P KuangScannerNet 准备就绪！
16:35:05.446 17 Y P Http 准备开始监听2个服务器
16:35:05.485 17 Y P Http 开始监听 tcp://0.0.0.0:8080
16:35:05.485 17 Y P Http 开始监听 tcp://[::]:8080
16:35:05.485 17 Y P Http 准备就绪！
