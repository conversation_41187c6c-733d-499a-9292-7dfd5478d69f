#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的AES解密脚本 - 基于伪代码分析
需要安装: pip install pycryptodome
"""

import base64
import json

def decrypt_with_pycryptodome():
    """使用pycryptodome库进行AES解密"""
    try:
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import unpad
        
        # 乡镇驿站扫描的加密数据
        encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
        
        # 从伪代码中发现的密钥
        key = "k301qsjbrh1s6ega"
        
        print("🔑 使用发现的密钥进行AES-ECB解密")
        print(f"密钥: {key}")
        print(f"密钥长度: {len(key)} 字节")
        
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted_data)
        print(f"Base64解码后长度: {len(encrypted_bytes)} 字节")
        
        # AES解密
        key_bytes = key.encode('utf-8')
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        
        # 解密
        decrypted_padded = cipher.decrypt(encrypted_bytes)
        
        # 尝试去除PKCS7填充
        try:
            decrypted = unpad(decrypted_padded, AES.block_size)
            print("✓ PKCS7填充去除成功")
        except ValueError:
            print("⚠️  PKCS7填充去除失败，使用原始解密数据")
            decrypted = decrypted_padded
        
        # 尝试UTF-8解码
        try:
            result_str = decrypted.decode('utf-8')
            print("✓ UTF-8解码成功!")
            print(f"解密结果长度: {len(result_str)}")
            print(f"解密结果: {result_str}")
            
            # 尝试解析JSON
            try:
                json_data = json.loads(result_str)
                print("\n✓ 解密结果是有效的JSON!")
                print("JSON内容:")
                print(json.dumps(json_data, indent=2, ensure_ascii=False))
                
                # 保存JSON结果
                with open('decrypted_success.json', 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                print("\n✓ JSON结果已保存到 decrypted_success.json")
                
                return json_data
                
            except json.JSONDecodeError:
                print("\n解密结果不是JSON格式")
                # 保存文本结果
                with open('decrypted_success.txt', 'w', encoding='utf-8') as f:
                    f.write(result_str)
                print("✓ 文本结果已保存到 decrypted_success.txt")
                
                return result_str
                
        except UnicodeDecodeError:
            print("❌ UTF-8解码失败")
            print(f"原始解密数据(hex): {decrypted[:64].hex()}...")
            return None
            
    except ImportError:
        print("❌ 需要安装pycryptodome库")
        print("请运行: pip install pycryptodome")
        return None
    except Exception as e:
        print(f"❌ 解密过程中出错: {e}")
        return None

def manual_analysis():
    """手动分析加密数据"""
    print("\n" + "="*60)
    print("手动分析 - 基于伪代码发现的信息")
    print("="*60)
    
    print("\n📋 伪代码分析结果:")
    print("1. 密钥: k301qsjbrh1s6ega (16字节)")
    print("2. 算法: AES-ECB")
    print("3. 编码: Base64")
    print("4. 流程: 原始数据 → AES-ECB加密 → Base64编码")
    print("5. 逆向: Base64解码 → AES-ECB解密 → 原始数据")
    
    print("\n🔍 关键代码位置:")
    print("- 第9756行: LODWORD(v46) = sub_41F461(1, \"k301qsjbrh1s6ega\", 0, -2147483644);")
    print("- 第9758行: v44 = \"AES-ECB\";")
    print("- 第9759行: v43 = sub_404606(...); // AES加密")
    print("- 第9768行: v42 = sub_406AF8(&v43); // Base64编码")
    
    print("\n💡 业务上下文:")
    print("- 用途: 乡镇驿站扫描数据加密")
    print("- 相关字段: [digest], [password], [reqTime], [token]")
    print("- 数据格式: 可能是JSON")

def main():
    """主函数"""
    print("正确的AES解密分析 - 乡镇驿站扫描数据")
    print("基于伪代码第9756-9768行的发现")
    print("="*60)
    
    # 尝试使用pycryptodome解密
    result = decrypt_with_pycryptodome()
    
    if result:
        print("\n🎉 解密成功!")
        if isinstance(result, dict):
            print("解密得到JSON数据，包含以下字段:")
            for key in result.keys():
                print(f"  - {key}")
        elif isinstance(result, str):
            print(f"解密得到文本数据，长度: {len(result)}")
    else:
        print("\n❌ 解密失败")
        
    # 显示手动分析结果
    manual_analysis()
    
    print("\n📝 下一步建议:")
    if not result:
        print("1. 确保安装了pycryptodome: pip install pycryptodome")
        print("2. 检查网络连接，重新安装库")
        print("3. 如果仍然失败，可能需要其他参数（如IV）")
    else:
        print("1. 分析解密后的数据结构")
        print("2. 理解各字段的业务含义")
        print("3. 测试其他加密数据样本")

if __name__ == "__main__":
    main()
