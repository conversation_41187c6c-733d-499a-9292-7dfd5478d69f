#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解密脚本 - 基于成功的反转密钥进行深度分析
"""

import base64
import json
import re
from Crypto.Cipher import AES

def clean_and_extract_json(data):
    """清理数据并提取JSON"""
    print("=== 数据清理和JSON提取 ===")
    
    # 尝试不同的方法提取JSON
    methods = [
        ("原始数据", data),
        ("去除控制字符", re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data)),
        ("只保留可打印字符", ''.join(c for c in data if c.isprintable())),
        ("去除非ASCII", ''.join(c for c in data if ord(c) < 128)),
    ]
    
    for method_name, cleaned_data in methods:
        print(f"\n--- {method_name} ---")
        print(f"长度: {len(cleaned_data)}")
        print(f"前100字符: {repr(cleaned_data[:100])}")
        
        # 寻找JSON模式
        json_patterns = [
            r'\{[^}]*\}',  # 简单的 {...}
            r'\{.*?\}',    # 贪婪匹配
            r'\{[\s\S]*\}', # 包含换行符
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, cleaned_data)
            if matches:
                print(f"找到 {len(matches)} 个JSON候选:")
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    print(f"  候选 {i+1}: {repr(match[:100])}")
                    try:
                        json_data = json.loads(match)
                        print(f"  ✓ 候选 {i+1} JSON解析成功!")
                        return match, json_data
                    except:
                        print(f"  - 候选 {i+1} JSON解析失败")
    
    return None, None

def analyze_binary_data(data):
    """分析二进制数据"""
    print("\n=== 二进制数据分析 ===")
    
    # 查找可能的字符串
    strings = []
    current_string = ""
    
    for byte in data:
        if 32 <= byte <= 126:  # 可打印ASCII
            current_string += chr(byte)
        else:
            if len(current_string) >= 3:  # 至少3个字符
                strings.append(current_string)
            current_string = ""
    
    if len(current_string) >= 3:
        strings.append(current_string)
    
    print(f"发现 {len(strings)} 个可能的字符串:")
    for i, s in enumerate(strings[:10]):  # 显示前10个
        print(f"  {i+1}: {repr(s)}")
    
    # 查找JSON关键字
    json_keywords = ['sessionId', 'token', 'digest', 'password', 'data', 'reqTime']
    found_keywords = [s for s in strings if any(kw in s for kw in json_keywords)]
    
    if found_keywords:
        print(f"\n发现JSON关键字:")
        for kw in found_keywords:
            print(f"  {repr(kw)}")

def try_different_key_processing(base_key, encrypted_bytes):
    """尝试不同的密钥处理方式"""
    print(f"\n=== 尝试不同的密钥处理方式 ===")
    
    # 反转密钥（已知有效）
    reversed_key = base_key[::-1]
    
    key_variants = [
        ("反转密钥", reversed_key),
        ("反转+填充", reversed_key.ljust(16, '0')),
        ("反转+重复", (reversed_key * 2)[:16]),
        ("反转+MD5", __import__('hashlib').md5(reversed_key.encode()).hexdigest()[:16]),
    ]
    
    for key_name, key in key_variants:
        print(f"\n--- {key_name}: {key} ---")
        
        try:
            key_bytes = key.encode('utf-8')[:16].ljust(16, b'\x00')
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            
            print(f"解密后长度: {len(decrypted)} 字节")
            
            # 尝试不同的编码
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    decoded = decrypted.decode(encoding)
                    
                    # 检查是否包含JSON特征
                    if '{' in decoded and ('"' in decoded or ':' in decoded):
                        print(f"  ✓ {encoding} 解码成功，包含JSON特征")
                        print(f"  前200字符: {repr(decoded[:200])}")
                        
                        # 尝试清理和提取JSON
                        json_text, json_data = clean_and_extract_json(decoded)
                        
                        if json_data:
                            print(f"  🎉 JSON解析成功!")
                            print(f"  字段: {list(json_data.keys())}")
                            
                            # 保存结果
                            with open(f'final_success_{key_name.replace(" ", "_")}.txt', 'w', encoding='utf-8') as f:
                                f.write(f"密钥: {key}\n")
                                f.write(f"编码: {encoding}\n")
                                f.write(f"原始解密结果:\n{decoded}\n\n")
                                f.write(f"清理后JSON:\n{json_text}")
                            
                            with open(f'final_success_{key_name.replace(" ", "_")}.json', 'w', encoding='utf-8') as f:
                                json.dump(json_data, f, indent=2, ensure_ascii=False)
                            
                            print(f"\nJSON内容:")
                            print(json.dumps(json_data, indent=2, ensure_ascii=False))
                            
                            return json_data
                        
                        # 如果JSON解析失败，分析二进制数据
                        analyze_binary_data(decrypted)
                        
                        # 保存原始解密结果
                        with open(f'raw_decrypt_{key_name.replace(" ", "_")}.txt', 'w', encoding=encoding) as f:
                            f.write(decoded)
                        print(f"  原始解密结果已保存")
                        
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    print(f"  {encoding} 处理失败: {e}")
                    
        except Exception as e:
            print(f"解密失败: {e}")
    
    return None

def main():
    """主函数"""
    print("最终解密分析 - 深度处理反转密钥结果")
    print("="*60)
    
    # 加密数据
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    # Base64解码
    encrypted_bytes = base64.b64decode(encrypted_data)
    print(f"Base64解码后长度: {len(encrypted_bytes)} 字节")
    
    # 基础密钥
    base_key = "k301qsjbrh1s6ega"
    
    # 尝试不同的密钥处理方式
    result = try_different_key_processing(base_key, encrypted_bytes)
    
    if result:
        print(f"\n🎉 最终解密成功!")
        print(f"这是乡镇驿站扫描的真实数据!")
    else:
        print(f"\n❌ 仍需进一步分析")
        print(f"但我们已经非常接近了，数据中确实包含JSON结构")

if __name__ == "__main__":
    main()
