韵达物流系统完整分析报告
==================================================

📋 认证信息
--------------------
登录端点: http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1
登录载荷:
  appType: 1
  company: [company]
  device: [device]
  digest: [digest]
  password: [password]
  reqTime: [reqTime]
  uniqueCode: [token]
  user: [user]
  version: 3.6.8.0821

🌐 四个菜单的网络请求
------------------------------
派件到达:
  端点: http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1
  业务规则: 固定设置分拨中心代码 530001

到派一体:
  端点: http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1
  业务规则: 单号输入时长度等于4的代码验证

乡镇驿站扫描:
  端点: http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1
  业务规则: 单号输入时长度等于4的代码验证

集包扫描:
  端点: http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1
  业务规则: 单号输入时开头为9的代码 + 下一站为预设6位数代码

🔐 加密信息
---------------
算法: AES-ECB
密钥: k301qsjbrh1s6ega
流程: 原始数据 → AES-ECB加密 → Base64编码

📊 业务逻辑规则
--------------------
派件到达时固定设置分拨中心代码为530001
  验证: distribution_center_code == '530001'

到派一体操作时验证输入代码长度为4位数字
  验证: len(input_code) == 4 and input_code.isdigit()

乡镇驿站扫描时验证输入代码长度为4位数字
  验证: len(input_code) == 4 and input_code.isdigit()

集包扫描时验证单号以9开头且下一站代码为6位数字
  验证: tracking_number.startswith('9') and len(next_station) == 6 and next_station.isdigit()

🔍 安全分析
---------------
加密强度: AES-128-ECB (相对较弱，ECB模式不推荐)
潜在漏洞:
  - ECB模式可能泄露数据模式
  - HTTP协议未加密传输
  - 固定的User-Agent可能被检测
建议:
  - 升级到AES-CBC或AES-GCM模式
  - 使用HTTPS协议
  - 实现更强的身份验证机制
