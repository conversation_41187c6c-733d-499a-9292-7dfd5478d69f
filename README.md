# 钉钉微信群消息转发器

这是一个自动将钉钉群消息转发到微信群的工具。

## 使用前准备

1. 确保已安装Python 3.6或更高版本
2. 安装所需依赖：
   ```
   pip install -r requirements.txt
   ```
3. 打开钉钉群"钉钉互动"和微信群"微微互动"的窗口
4. 调整窗口位置，确保两个窗口都可见且不重叠

## 使用方法

1. 运行程序：
   ```
   python dingtalk_wechat_forwarder.py
   ```
2. 程序会自动每5秒检查一次钉钉群的新消息
3. 发现新消息时会自动转发到微信群
4. 按Ctrl+C可以停止程序

## 注意事项

1. 运行程序时请勿移动鼠标，以免影响自动化操作
2. 如果程序无法正常工作，可能需要调整代码中的坐标值
3. 建议在运行程序前先测试一下消息复制和发送功能
