import pywinauto
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 消息记录，避免重复处理
processed_dingtalk_messages = set()
processed_wechat_messages = set()

# 消息筛选条件（可以根据需要修改）
def should_forward_wechat_message(message, sender):
    # 示例：只转发包含特定关键词的消息
    keywords = ["重要", "紧急", "请回复", "通知"]
    return any(keyword in message for keyword in keywords)

def connect_to_apps():
    """连接到钉钉和微信应用"""
    try:
        # 连接到钉钉
        dingtalk = pywinauto.Application(backend="uia").connect(title_re="钉钉")
        dingtalk_window = dingtalk.window(title_re="钉钉")
        
        # 连接到微信
        wechat = pywinauto.Application(backend="uia").connect(title_re="微信")
        wechat_window = wechat.window(title_re="微信")
        
        return dingtalk_window, wechat_window
    except Exception as e:
        logger.error(f"连接应用失败: {e}")
        return None, None

def get_dingtalk_messages(dingtalk_window, group_name):
    """获取钉钉群的最新消息"""
    try:
        # 打印钉钉窗口的控件结构
        logger.info("钉钉窗口控件结构:")
        dingtalk_window.print_control_identifiers()
        
        # 获取消息区域
        message_area = dingtalk_window.child_window(control_type="Custom", class_name="conversation-message-list")
        if not message_area.exists():
            message_area = dingtalk_window.child_window(control_type="Custom", class_name="MessageList")
            if not message_area.exists():
                # 尝试其他可能的控件类型
                message_area = dingtalk_window.child_window(control_type="Pane")
                if not message_area.exists():
                    message_area = dingtalk_window.child_window(control_type="Document")
                    if not message_area.exists():
                        logger.error("无法找到钉钉消息区域")
                        return []
        
        # 获取所有消息元素
        message_elements = message_area.children(control_type="Text")
        logger.info(f"找到 {len(message_elements)} 条钉钉消息")
        
        # 提取消息内容和发送者
        messages = []
        for i in range(len(message_elements) - 1, -1, -1):
            try:
                # 尝试获取消息内容
                message_text = message_elements[i].window_text()
                
                # 尝试获取发送者（可能在相邻元素中）
                sender = "未知用户"
                if i > 0:
                    sender_element = message_elements[i-1]
                    if ":" not in sender_element.window_text():
                        sender = sender_element.window_text()
                
                # 创建消息标识符（发送者+消息内容）
                message_id = f"{sender}:{message_text}"
                
                # 如果是新消息，添加到结果中
                if message_id not in processed_dingtalk_messages:
                    messages.append({
                        "sender": sender,
                        "content": message_text,
                        "id": message_id
                    })
                    processed_dingtalk_messages.add(message_id)
                    logger.info(f"发现新钉钉消息: {message_id}")
            except Exception as e:
                logger.warning(f"处理钉钉消息元素时出错: {e}")
        
        return messages
    except Exception as e:
        logger.error(f"获取钉钉消息失败: {e}")
        return []

def send_to_wechat(wechat_window, group_name, message):
    """发送消息到微信群"""
    try:
        # 确保微信窗口处于活动状态
        wechat_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = wechat_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 找到输入框
        input_box = wechat_window.child_window(control_type="Edit")
        input_box.set_focus()
        
        # 清空输入框
        input_box.type_keys("^a{BACKSPACE}")
        
        # 输入消息并发送
        input_box.type_keys(message, with_spaces=True)
        input_box.type_keys("{ENTER}")
        
        logger.info(f"已发送消息到微信群: {message}")
        return True
    except Exception as e:
        logger.error(f"发送微信消息失败: {e}")
        return False

def get_wechat_messages(wechat_window, group_name):
    """获取微信群的最新消息"""
    try:
        # 获取消息区域
        message_area = wechat_window.child_window(title="消息", control_type="List")
        if not message_area.exists():
            logger.error("无法找到微信消息区域")
            return []
        
        # 获取所有消息元素
        message_elements = message_area.children(control_type="ListItem")
        logger.info(f"找到 {len(message_elements)} 条微信消息")
        
        # 提取消息内容和发送者
        messages = []
        for element in message_elements:
            try:
                # 直接获取消息文本
                message_text = element.window_text()
                if not message_text:
                    continue
                
                # 解析消息文本
                parts = message_text.split('\n')
                if len(parts) >= 2:
                    sender = parts[0].strip()
                    content = '\n'.join(parts[1:]).strip()
                else:
                    sender = "未知用户"
                    content = message_text
                
                # 创建消息标识符
                message_id = f"{sender}:{content}"
                
                # 如果是新消息，添加到结果中
                if message_id not in processed_wechat_messages:
                    messages.append({
                        "sender": sender,
                        "content": content,
                        "id": message_id
                    })
                    processed_wechat_messages.add(message_id)
                    logger.info(f"发现新微信消息: {message_id}")
            except Exception as e:
                logger.warning(f"处理微信消息元素时出错: {e}")
        
        return messages
    except Exception as e:
        logger.error(f"获取微信消息失败: {e}")
        return []

def send_to_dingtalk(dingtalk_window, group_name, message):
    """发送消息到钉钉群"""
    try:
        # 确保钉钉窗口处于活动状态
        dingtalk_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = dingtalk_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 找到输入框
        input_box = dingtalk_window.child_window(control_type="Edit")
        input_box.set_focus()
        
        # 清空输入框
        input_box.type_keys("^a{BACKSPACE}")
        
        # 输入消息并发送
        input_box.type_keys(message, with_spaces=True)
        input_box.type_keys("{ENTER}")
        
        logger.info(f"已发送消息到钉钉群: {message}")
        return True
    except Exception as e:
        logger.error(f"发送钉钉消息失败: {e}")
        return False

def main():
    # 配置群聊名称
    dingtalk_group_name = "钉微互动"
    wechat_group_name = "中通出港群-云华蜜蜂乐园"
    
    # 连接到应用
    dingtalk_window, wechat_window = connect_to_apps()
    if not dingtalk_window or not wechat_window:
        logger.error("无法连接到应用，程序退出")
        return
    
    logger.info("成功连接到钉钉和微信")
    
    try:
        while True:
            # 获取钉钉最新消息并转发到微信
            dingtalk_messages = get_dingtalk_messages(dingtalk_window, dingtalk_group_name)
            for message in dingtalk_messages:
                formatted_message = f"【钉钉消息】来自 {message['sender']}：{message['content']}"
                send_to_wechat(wechat_window, wechat_group_name, formatted_message)
            
            # 获取微信最新消息并有条件地转发到钉钉
            wechat_messages = get_wechat_messages(wechat_window, wechat_group_name)
            for message in wechat_messages:
                # 应用筛选条件
                if should_forward_wechat_message(message['content'], message['sender']):
                    formatted_message = f"【微信消息】来自 {message['sender']}：{message['content']}"
                    send_to_dingtalk(dingtalk_window, dingtalk_group_name, formatted_message)
            
            # 等待一段时间再检查新消息
            time.sleep(5)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()