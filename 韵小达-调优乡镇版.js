"ui"; // 声明使用UI模式，启用图形界面支持

importClass(java.io.File); // 导入Java的File类，用于文件操作和目录遍历

// 全局变量定义
var selectedFile = null; // 存储用户选择的单号文件路径
var mainActivity = null; // 保存主界面引用，用于界面切换和恢复
var running = false; // 脚本运行状态标志，控制脚本是否在执行中
var currentProgress = 0; // 当前处理进度，用于断点续传功能
var hasBeenPaused = false; // 是否暂停过的标志，用于决定是否显示断点续传选项

// 性能监控相关变量
var performanceStats = {
  startTime: 0,          // 脚本开始时间
  processedCount: 0,     // 处理的单号数量
  lastCheckTime: 0,      // 上次检查时间
  lastProcessedCount: 0, // 上次检查时处理的单号数量
  totalSpeed: 0,         // 总体处理速度（单号/秒）
  currentSpeed: 0        // 当前处理速度（单号/秒）
};

// 内存管理相关变量
var memoryManager = {
  lastCleanTime: 0,      // 上次清理内存的时间
  cleanInterval: 15000,  // 清理内存的时间间隔（减半为15秒）
  baseCleanInterval: 15000, // 基础清理间隔
  performanceInterval: 30000, // 性能显示的时间间隔（30秒）
  processedSinceLastClean: 0, // 自上次清理后处理的单号数量
  cleanThreshold: 100    // 处理这么多单号后触发清理
};

// 检测当前是否是打包环境
function isPackagedEnvironment() {
  try {
    var packageName = context.getPackageName();
    // 如果包名是"com.yxd"，则是打包环境
    return packageName === "com.yxd";
  } catch(e) {
    // 如果获取包名出错，默认不是打包环境
    return false;
  }
}

// 清理缓存和临时文件
function cleanupFiles() {
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  if (files.exists(jl_path)) {
    files.remove(jl_path);
  }
  console.clear();
  return 0;
}

// 清理内存并释放资源
function cleanMemory() {
  // 直接尝试调用GC
  if (global.gc) {
    global.gc();
  } else {
    // 减少创建的数组大小和次数，降低内存压力
    for (var i = 0; i < 5; i++) {
      var temp = new Array(500000);
      temp = null;
    }
  }

  // 手动触发一次小型GC
  var temp = {};
  for (var i = 0; i < 1000; i++) {
    temp[i] = i;
  }
  temp = null;

  memoryManager.lastCleanTime = Date.now();
}

// 初始化性能统计
function initPerformanceStats(currentCount) {
  var now = Date.now();
  performanceStats.startTime = now;
  performanceStats.lastCheckTime = now;
  performanceStats.lastProcessedCount = currentCount;
}

// 初始化界面元素，创建主界面UI
ui.layout(
  <vertical bg="#ffffff" padding="16" marginTop="5">
    <text text="自动化工具启动界面" textSize="24sp" textColor="#000000" gravity="center" typeface="serif"/>
    <button id="selectFileBtn" text="请选择单号文件" marginTop="16" style="Widget.AppCompat.Button.Colored" textSize="18sp" typeface="serif"/>
    <list id="fileList" h="320" marginTop="8">
      <horizontal>
        <text id="fileName" text="{{fileName}}" textColor="#000000" textSize="18sp" padding="8" typeface="serif"/>
      </horizontal>
    </list>
    <text
      id="selectedFileText"
      text="已选择单号文件："
      textColor="#ffffff"
      bg="#4CAF50"
      padding="8"
      marginTop="12"
      textSize="18sp"
      typeface="serif"
      lines="2"
      ellipsize="end"
      w="*"
      h="auto"
      gravity="left"
    />
    <horizontal marginTop="24" gravity="center">
      <button id="btnJibao" text="韵达集包" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnDaopai" text="韵达到派" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnXiangzhen" text="乡镇优化" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" textSize="18sp" typeface="serif"/>
    </horizontal>
  </vertical>
);

// 加载 txt 文件列表
function loadTxtFiles() {
  let dir = new File("/sdcard/Pictures");
  if (!dir.exists() || !dir.isDirectory()) {
    toast("目标文件夹不存在！");
    return [];
  }

  let files = dir.listFiles();
  let txtFiles = [];

  if (files) {
    for (let f of files) {
      if (f.isFile() && f.getName().endsWith(".txt")) {
        txtFiles.push({
          fileName: f.getName(),
          fullPath: f.getAbsolutePath()
        });
      }
    }
  }
  return txtFiles;
}

// 文件选择按钮点击逻辑
ui.selectFileBtn.on("click", () => {
  let fileList = loadTxtFiles();
  if (fileList.length === 0) {
    toast("未找到 txt 文件");
  } else {
    ui.fileList.setDataSource(fileList);
  }
});

// 文件列表项点击事件
ui.fileList.on("item_click", (item) => {
  selectedFile = item.fullPath;
  ui.selectedFileText.setText("已选择单号文件：\n" + item.fileName);
  toast("已选择: " + item.fileName);
});

// 功能按钮点击事件
ui.btnJibao.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");
  toast("韵达集包功能已禁用");
});

ui.btnDaopai.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");
  toast("韵达到派功能已禁用");
});

ui.btnXiangzhen.on("click", () => {
  if (!selectedFile) return toast("请先选择单号文件！");
  toast("启动乡镇优化");
  mainActivity = activity;
  setTimeout(function() {
    launchFloatingWindow(selectedFile, "xiangzhen");
    ui.setContentView(ui.inflate(<frame/>));
  }, 500);
});

// 统一的悬浮窗启动函数
function launchFloatingWindow(filePath, mode) {
  // 创建包含三个按钮的悬浮窗：启动、暂停和关闭
  var window = floaty.window(
    <horizontal>
      <button id="start" text="启动" w="80" h="50" />
      <button id="pause" text="暂停" w="80" h="50" />
      <button id="closeBtn" text="关闭" w="80" h="50" />
    </horizontal>
  );

  window.setPosition(10, 10);
  setInterval(() => {}, 1000);

  running = false;
  var scriptThread = null;
  currentProgress = 0;
  hasBeenPaused = false;
  var currentStartOptionsWindow = null;

  // 启动按钮点击事件处理
  window.start.click(() => {
    if (!running) {
      if (currentStartOptionsWindow) {
        currentStartOptionsWindow.close();
        currentStartOptionsWindow = null;
        return;
      }

      let startOptionsWindow = floaty.window(
        <vertical bg="#00000000" padding="0" w="100">
          <button id="fromStartBtn" text="从头开始" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
          <button id="continueBtn" text="断点继续" bg="#4CAF50" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        </vertical>
      );

      currentStartOptionsWindow = startOptionsWindow;
      startOptionsWindow.setPosition(17, 105);

      // 从头开始按钮点击事件
      startOptionsWindow.fromStartBtn.click(() => {
        startOptionsWindow.close();
        currentStartOptionsWindow = null;
        running = true;
        toast("从头开始运行脚本");
        currentProgress = 0;

        scriptThread = threads.start(function () {
          console.show();
          console.setPosition(5, 825);
          var fileName = filePath.split("/").pop().replace(".txt", "");
          console.setTitle(fileName);

          // 根据模式选择执行的脚本
          if (mode === "xiangzhen") {
            runXiangzhenScript(filePath, 0);
          }
        });
      });

      // 断点继续按钮点击事件
      startOptionsWindow.continueBtn.click(() => {
        startOptionsWindow.close();
        currentStartOptionsWindow = null;
        running = true;
        toast("断点继续运行脚本");

        var jl_path = "/sdcard/Pictures/记录文本.txt";
        var lastProgress = 0;

        if (files.exists(jl_path)) {
          try {
            var content = files.read(jl_path);
            var lines = content.split("\n").filter(line => line.trim() !== "");
            if (lines.length > 0) {
              lastProgress = parseInt(lines[lines.length - 1]);
              if (isNaN(lastProgress)) {
                lastProgress = 0;
              }
            }
          } catch (e) {
            // 静默处理错误
          }
        }

        currentProgress = lastProgress;

        scriptThread = threads.start(function () {
          console.show();
          console.setPosition(5, 825);
          var fileName = filePath.split("/").pop().replace(".txt", "");
          console.setTitle(fileName);

          // 根据模式选择执行的脚本
          if (mode === "xiangzhen") {
            runXiangzhenScript(filePath, currentProgress);
          }
        });
      });
    }
  });

  // 暂停按钮点击事件处理
  window.pause.click(() => {
    if (running) {
      running = false;
      hasBeenPaused = true;
      toast("脚本已暂停");

      var jl_path = "/sdcard/Pictures/记录文本.txt";
      try {
        files.write(jl_path, (currentProgress + 1) + "\n");
        console.warn("【暂停】已保存断点位置: " + (currentProgress + 1));
      } catch (e) {
        // 静默处理错误
      }

      if (scriptThread) {
        scriptThread.interrupt();
      }
      threads.shutDownAll();

      setTimeout(function() {
        toast("脚本已完全停止");
      }, 500);
    }
  });

  // 关闭按钮点击事件处理
  var currentCloseOptionsWindow = null;
  window.closeBtn.click(() => {
    if (currentCloseOptionsWindow) {
      currentCloseOptionsWindow.close();
      currentCloseOptionsWindow = null;
      return;
    }

    let optionsWindow = floaty.window(
      <vertical bg="#00000000" padding="0" w="100">
        <button id="returnBtn" text="返回选择" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        <button id="exitBtn" text="退出脚本" bg="#F44336" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
      </vertical>
    );

    currentCloseOptionsWindow = optionsWindow;
    optionsWindow.setPosition(17, 105);

    // 返回选择按钮点击事件
    optionsWindow.returnBtn.click(() => {
      toast("返回主界面");
      optionsWindow.close();
      currentCloseOptionsWindow = null;
      window.close();
      console.hide();

      running = false;
      currentProgress = 0;
      hasBeenPaused = false;
      threads.shutDownAll();

      setTimeout(function() {
        var isPackaged = isPackagedEnvironment();
        var currentSource = engines.myEngine().getSource();
        toast("返回主界面");

        if (isPackaged) {
          var packageName = context.getPackageName();
          app.launch(packageName);
          setTimeout(function() {
            exit();
          }, 1000);
        } else {
          if (currentSource) {
            engines.execScriptFile(currentSource);
            setTimeout(function() {
              engines.myEngine().forceStop();
            }, 500);
          } else {
            toast("返回主界面失败，请手动重启");
            exit();
          }
        }
      }, 300);
    });

    // 退出脚本按钮点击事件
    optionsWindow.exitBtn.click(() => {
      toast("脚本已退出");
      var cleanedCount = cleanupFiles();
      if (cleanedCount > 0) {
        console.warn("【退出】已清理" + cleanedCount + "个缓存文件");
      }

      // 使用资源清理函数
      cleanupResources();

      optionsWindow.close();
      currentCloseOptionsWindow = null;
      window.close();
      console.hide();
      threads.shutDownAll();
      exit();
    });
  });
}

// 资源清理函数
function cleanupResources() {
  // 清理所有临时变量
  performanceStats = {
    startTime: 0,
    processedCount: 0,
    lastCheckTime: 0,
    lastProcessedCount: 0,
    totalSpeed: 0,
    currentSpeed: 0
  };

  // 强制GC
  if (global.gc) {
    global.gc();
  }

  // 清理文件资源
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  if (files.exists(jl_path)) {
    files.remove(jl_path);
    console.warn("【退出】已删除记录文件");
  }

  // 清空控制台
  console.clear();
}

// ============ 核心功能脚本 ============

// 乡镇优化脚本
function runXiangzhenScript(filePath, startIndex) {
  // 记录文件路径，用于保存处理进度
  var jl_path = "/sdcard/Pictures/记录文本.txt";
  var jl_hs = 0;

  // 创建或打开记录文件
  if (files.create(jl_path)) {
    sleep(150);
  } else {
    try {
      var arr_jl = open(jl_path).readlines();
      jl_hs = arr_jl.length === 0 ? 0 : parseInt(arr_jl[arr_jl.length - 1]) + 1;
    } catch (e) {
      console.warn("【警告】读取记录文件失败: " + e.message);
      try {
        files.remove(jl_path);
        files.create(jl_path);
        console.info("【信息】已重新创建记录文件");
      } catch (err) {
        console.error("【错误】无法创建记录文件: " + err.message);
      }
      jl_hs = 0;
    }
  }

  var startPos = (startIndex !== undefined && startIndex > 0) ? startIndex : jl_hs;

  // 读取文件并过滤空行
  var rawLines = open(filePath).readlines();
  var arr_a = [];

  // 过滤空行和只包含空白字符的行
  for (var j = 0; j < rawLines.length; j++) {
    if (rawLines[j] && rawLines[j].trim() !== "") {
      arr_a.push(rawLines[j].trim());
    }
  }

  if (startIndex === 0) {
    console.log("【开始】总数: " + arr_a.length);
  }

  var now = Date.now();
  // 使用初始化函数
  initPerformanceStats(startPos);
  performanceStats.processedCount = startPos;
  memoryManager.lastCleanTime = now;

  for (var i = startPos; i < arr_a.length; i++) {
    if (!running) {
      console.warn("【暂停】" + arr_a[i]);
      console.warn("【暂停】" + (i + 1) + "/" + arr_a.length);
      break;
    }

    console.warn("【处理】" + arr_a[i]);
    console.warn("【处理】" + (i + 1) + "/" + arr_a.length);

    // 获取当前单号
    var currentNumber = arr_a[i].trim();

    // 乡镇优化核心功能 - 根据单号长度决定输入到哪个字段
    if (currentNumber.length < 6) {
      // 单号长度小于6，输入到站点代码字段
      var stationCodeField = id("shipment_business_shipment_give_shipment_station_code").findOne();

      // 只设置站点代码，不点击添加按钮
      stationCodeField.setText(currentNumber);
      sleep(10);

      // 检查是否有确认按钮并点击
      var cx = text("确定").findOne(170);
      if (cx !== null) {
        cx.click();
      }
    } else {
      // 单号长度大于等于6，输入到单号字段
      var billNumberField = id("shipment_business_shipment_give_shipment_bill_number").findOne();
      var addButton = id("shipment_business_shipment_give_shipment_add").findOne();

      // 设置单号并点击添加按钮
      billNumberField.setText(currentNumber);
      sleep(15);
      addButton.click();
      sleep(15);

      // 检查是否有确认按钮并点击
      var cx = text("确定").findOne(160);
      if (cx !== null) {
        cx.click();
      }
    }

    files.append(jl_path, i + "\n");
    currentProgress = i;

    var now = Date.now();

    // 只在30秒间隔时计算和显示性能数据
    if (now - performanceStats.lastCheckTime >= memoryManager.performanceInterval) {
      var countDiff = (i - startPos + 1) - performanceStats.lastProcessedCount;
      var timeSinceLastCheck = (now - performanceStats.lastCheckTime) / 1000;

      if (timeSinceLastCheck > 0) {
        performanceStats.currentSpeed = (countDiff / timeSinceLastCheck).toFixed(2);
      }

      // 只在控制台显示一次性能数据
      console.info("【性能】当前速度: " + performanceStats.currentSpeed + " 单号/秒");

      performanceStats.lastCheckTime = now;
      performanceStats.lastProcessedCount = i - startPos + 1;

      // 根据处理速度动态调整清理间隔
      if (performanceStats.currentSpeed > 3) {
        // 处理速度快时(>3单号/秒)，减少清理频率（增加间隔）
        memoryManager.cleanInterval = Math.min(30000, memoryManager.baseCleanInterval + 5000);
      } else if (performanceStats.currentSpeed <= 1) {
        // 处理速度很慢时(≤1单号/秒)，显著增加清理频率（大幅减少间隔）
        memoryManager.cleanInterval = Math.max(5000, memoryManager.baseCleanInterval - 7500);
      } else {
        // 处理速度一般(1-3单号/秒)，适度增加清理频率
        memoryManager.cleanInterval = Math.max(10000, memoryManager.baseCleanInterval - 2500);
      }
    }

    // 保持原有的内存清理触发机制不变
    memoryManager.processedSinceLastClean++;
    if (memoryManager.processedSinceLastClean >= memoryManager.cleanThreshold ||
        now - memoryManager.lastCleanTime >= memoryManager.cleanInterval) {
      cleanMemory();
      memoryManager.processedSinceLastClean = 0;
    }
  }

  if (i >= arr_a.length) {
    console.log("【完成】" + arr_a[arr_a.length-1]);
    console.log("【完成】" + arr_a.length + "/" + arr_a.length);
    console.log("【完成】全部单号处理完成");
    // 完成时强制显示最终性能统计
    var now = Date.now();
    var totalTime = (now - performanceStats.startTime) / 1000;
    var totalCount = arr_a.length - startPos;
    var avgSpeed = totalTime > 0 ? (totalCount / totalTime).toFixed(2) : 0;
    console.info("【性能】平均速度: " + avgSpeed + " 单号/秒");
  }
}



