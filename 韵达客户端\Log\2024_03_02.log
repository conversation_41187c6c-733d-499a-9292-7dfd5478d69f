﻿#Software: 韵达自动化扫描客户端
#ProcessID: 6536 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,735M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-02
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:19:14.327  5 Y P KuangScannerNet 准备开始监听4个服务器
06:19:14.342  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:19:14.346  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:19:14.413  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:19:14.462  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:19:14.462  5 Y P KuangScannerNet 准备就绪！
06:19:14.478  5 Y P Http 准备开始监听2个服务器
06:19:14.501  5 Y P Http 开始监听 tcp://0.0.0.0:8080
06:19:14.526  5 Y P Http 开始监听 tcp://[::]:8080
06:19:14.526  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 4664 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,522M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-03-02
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:35:15.671  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
11:35:15.674  1 N - NewLife组件核心库 ©2002-2022 NewLife
11:35:15.674  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
11:35:15.674  1 N - 韵达自动化扫描客户端 Copyright ©  2022
11:35:15.675  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
11:35:15.767  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 13768 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,648M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-02
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
11:57:08.216  7 Y P KuangScannerNet 准备开始监听4个服务器
11:57:08.389  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
11:57:08.420  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
11:57:08.425  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
11:57:08.428  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
11:57:08.429  7 Y P KuangScannerNet 准备就绪！
11:57:08.443  7 Y P Http 准备开始监听2个服务器
11:57:08.444  7 Y P Http 开始监听 tcp://0.0.0.0:8080
11:57:08.459  7 Y P Http 开始监听 tcp://[::]:8080
11:57:08.459  7 Y P Http 准备就绪！
