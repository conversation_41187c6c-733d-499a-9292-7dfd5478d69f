🔍【通用结构】
Fiddler Inspectors 分为两部分：

Request（请求）在上半部分

Response（响应）在下半部分

每个部分下面都有这些标签：Headers、TextView、JSON 等

📁 各标签说明（从上到下）
1️⃣ Headers
显示 HTTP 的头部信息。

既适用于请求（如请求头）、也适用于响应（如服务器返回的头部信息）。

常见用途：

查看 Host、User-Agent、Authorization（Token）、Content-Type 等

查看服务器返回的 Set-Cookie、Content-Length、状态码（如 200 OK）

2️⃣ TextView
将请求或响应体显示为普通文本（适合看 HTML、JSON、字符串等）

常见用途：

看 JSON 或 HTML 内容（如果不需要高亮）

快速复制查看大段文本

3️⃣ SyntaxView
类似 TextView，但会高亮语法（适用于 JSON、HTML、XML）

如果返回的内容结构复杂，看这里更清晰。

4️⃣ WebForms
如果请求的 Content-Type 是 application/x-www-form-urlencoded，它会以表单方式展示参数。

常见用途：

查看 POST 表单参数，如：username=abc&password=123 会显示成表格样式。

5️⃣ HexView
显示原始二进制内容的十六进制形式。

常见用途：

请求/响应中包含文件（如图片、音频、PDF）时查看原始字节数据。

数据被加密时，确认格式、长度。

6️⃣ Auth
显示身份验证信息（如 HTTP Basic、Bearer Token 等）

常见用途：

快速查看是否使用了认证头部

分析登录请求是否带 Token

7️⃣ Cookies
展示请求中发送或响应中接收的 Cookie 信息。

常见用途：

查看 Cookie 内容（如 JSESSIONID, token, sid 等）

分析会话/登录状态是否通过 Cookie 管控

8️⃣ Raw
原始视图，完整显示 HTTP 报文内容（包括头 + 正文）

常见用途：

查看完整 HTTP 请求格式

分析协议细节（包括换行符、大小写等）

9️⃣ JSON
格式化查看 JSON 内容（前提是请求/响应体为 JSON）

常见用途：

自动缩进结构，便于阅读复杂 JSON 数据

适合接口开发、调试、逆向

🔟 XML
如果返回内容是 XML 格式，这里会格式化显示（自动缩进、折叠）

常见用途：



你想看什么	推荐标签
请求/响应头信息	Headers
登录凭证（Token、Cookie）	Headers + Cookies + Auth
表单提交的参数	WebForms
JSON 返回结果	JSON（或 TextView / SyntaxView）
查看完整报文	Raw
文件类数据、加密内容	HexView

下半部分 13 个标签详细解释：
1️⃣ Transformer
用途： 这个标签并不是标准的 Fiddler 标签，可能是某个扩展或自定义插件提供的功能。一般来说，它用于将请求或响应进行转换、格式化或其他数据处理。

常见场景：

如果你需要对请求或响应做某些转换（如编码转换、解码等），可以通过这个选项查看。

2️⃣ Headers
用途： 显示请求和响应的 HTTP 头部信息。

常用场景：

查看请求头中的 Authorization（Token）、Content-Type（数据格式）、User-Agent（客户端信息）。

查看响应头中的 Set-Cookie（会话信息）、Cache-Control（缓存控制）。

3️⃣ TextView
用途： 将响应体或请求体显示为普通文本。

常见场景：

用于查看返回的 HTML 或简单的文本内容。

如果响应内容为字符串或非结构化数据，使用 TextView 可以直观显示。

4️⃣ SyntaxView
用途： 类似于 TextView，但是带有语法高亮，适合用于显示结构化数据（如 JSON、XML、HTML 等）。

常见场景：

当响应为 JSON 或 XML 数据时，使用 SyntaxView 可以帮助你清晰地查看其结构。

比如接口返回的数据是 JSON 时，SyntaxView 会高亮字段，帮助你快速分析。

5️⃣ ImageView
用途： 显示图像数据。

常见场景：

如果请求返回的是图片或图像数据，这个标签可以直接显示图像，而不是原始的二进制数据。

适合查看 API 返回的图片文件（如 PNG、JPEG）。

6️⃣ HexView
用途： 以十六进制格式显示响应体的数据，适用于查看二进制内容。

常见场景：

如果响应数据为二进制格式（如文件下载），或者你正在调试加密数据，这个标签非常有用。

查看图片、音频文件或其他二进制数据时可以使用。

7️⃣ WebView
用途： 在浏览器中打开响应内容（如果响应内容是 HTML 或支持 Web 内容）。

常见场景：

查看页面的视觉效果，尤其是 API 返回的 HTML 内容。

适用于调试 Web 应用时，查看返回的 HTML 页面和样式。

8️⃣ Auth
用途： 显示与请求相关的认证信息。

常见场景：

查看请求头中的认证信息，例如 Basic Auth 或 Bearer Token。

分析请求是否需要身份验证或是否已携带有效的凭证。

9️⃣ Caching
用途： 显示与缓存相关的信息（如缓存控制头、ETag、Last-Modified 等）。

常见场景：

分析服务器是否对数据进行了缓存。

检查是否使用了 Cache-Control，或者是否在响应中返回了缓存标识（如 ETag）。

🔟 Cookies
用途： 显示请求和响应中的 Cookie 信息。

常见场景：

查看请求中是否携带了 Cookie（如 JSESSIONID、token）。

查看服务器返回的 Cookie，判断是否有会话管理或身份验证相关的 Cookie。

1️⃣1️⃣ Raw
用途： 显示完整的 HTTP 请求和响应报文（包括头部和体内容）。

常见场景：

查看完整的请求/响应报文格式，尤其是在调试 HTTP 协议时。

分析协议细节，查看是否有意外的格式问题。

1️⃣2️⃣ JSON
用途： 如果响应是 JSON 格式，使用该标签查看响应内容，Fiddler 会自动格式化 JSON 数据。

常见场景：

查看接口返回的 JSON 数据，帮助你快速定位返回的数据结构。

如果接口返回的字段很多，使用 JSON 可以更清晰地查看字段值。

1️⃣3️⃣ XML
用途： 如果响应是 XML 格式，使用该标签查看 XML 数据，Fiddler 会自动格式化并缩进。

常见场景：

查看返回的 XML 数据，帮助你理解 XML 中的元素、属性和结构。

如果 API 返回的是 XML 格式的内容（如 SOAP Web 服务），则 XML 标签非常有用。


