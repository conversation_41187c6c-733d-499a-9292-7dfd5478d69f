#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析抓包得到的加密数据
"""

import base64
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

def analyze_encrypted_data():
    """分析加密数据"""
    
    # 抓包得到的加密数据
    encrypted_data = "UQhCyTmwY0Lw8z7033bgt3ZKn3inT3NGKGow3UNxKg2Vzpd8uN1ZPf4EFYNs3JVcQg/cTX1rVCMOxGMkoqKP3LVsxbzWzxvdD/IdR6XLe9aJ3G7hMJ2pByxW815ZNT+OlQa0Qxw80fL2SqC7huDJiEeCS6RkZtW81mcJOalBEXzoEC8quJUCepvoFDWcR+8Ms8P9ek7Lp4kCr14hHHoZRX7CVTVM6DXCgi6NRSQcm7XSZFSezhRy4piVXDGIqbwnjRA/sJsx+cdrUcPm0g5hcw=="
    
    print("🔍 加密数据分析")
    print("=" * 80)
    print(f"原始数据长度: {len(encrypted_data)} 字符")
    
    try:
        # Base64解码
        decoded_data = base64.b64decode(encrypted_data)
        print(f"Base64解码后长度: {len(decoded_data)} 字节")
        print(f"解码后数据 (hex): {decoded_data.hex()}")
        
        # 分析数据结构
        print(f"\n📊 数据结构分析:")
        print(f"- 数据长度: {len(decoded_data)} 字节")
        print(f"- AES块大小: 16 字节")
        print(f"- 数据块数: {len(decoded_data) // 16}")
        print(f"- 是否为AES块的整数倍: {'是' if len(decoded_data) % 16 == 0 else '否'}")
        
        # 尝试使用已知密钥解密
        aes_key = "k301qsjbrh1s6ega"
        print(f"\n🔐 尝试使用密钥解密: {aes_key}")
        
        try:
            cipher = AES.new(aes_key.encode('utf-8'), AES.MODE_ECB)
            decrypted_data = cipher.decrypt(decoded_data)
            
            # 尝试去除填充
            try:
                unpadded_data = unpad(decrypted_data, AES.block_size)
                decrypted_text = unpadded_data.decode('utf-8')
                print(f"✅ 解密成功!")
                print(f"解密结果: {decrypted_text}")
                
                # 尝试解析为JSON
                try:
                    json_data = json.loads(decrypted_text)
                    print(f"\n📄 JSON解析成功:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                    return json_data
                except json.JSONDecodeError:
                    print(f"⚠️  不是有效的JSON格式")
                    return decrypted_text
                    
            except ValueError as e:
                print(f"❌ 填充去除失败: {e}")
                # 尝试直接解码
                try:
                    decrypted_text = decrypted_data.decode('utf-8', errors='ignore')
                    print(f"部分解密结果: {decrypted_text}")
                except:
                    print(f"解密数据 (hex): {decrypted_data.hex()}")
                    
        except Exception as e:
            print(f"❌ AES解密失败: {e}")
            
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")
        return None

def test_encrypt_tracking_number():
    """测试加密快递单号"""
    
    print("\n🧪 测试快递单号加密")
    print("-" * 50)
    
    tracking_number = "312799097135367"
    aes_key = "k301qsjbrh1s6ega"
    
    try:
        from Crypto.Util.Padding import pad
        
        cipher = AES.new(aes_key.encode('utf-8'), AES.MODE_ECB)
        padded_data = pad(tracking_number.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')
        
        print(f"原始快递单号: {tracking_number}")
        print(f"加密结果: {encrypted_b64}")
        print(f"加密长度: {len(encrypted_b64)} 字符")
        
    except Exception as e:
        print(f"❌ 加密失败: {e}")

def analyze_data_structure():
    """分析数据可能的结构"""
    
    print("\n🏗️  推测数据结构")
    print("-" * 50)
    
    print("基于抓包数据的长度和特征，这很可能是:")
    print("1. 完整的HTTP请求体 (JSON格式)")
    print("2. 包含多个字段的加密数据")
    print("3. 可能包含:")
    print("   - 认证信息 (用户名、密码、设备信息)")
    print("   - 业务数据 (快递单号、操作类型)")
    print("   - 时间戳和token")
    print("   - 其他元数据")
    
    # 计算可能的JSON大小
    encrypted_length = len("UQhCyTmwY0Lw8z7033bgt3ZKn3inT3NGKGow3UNxKg2Vzpd8uN1ZPf4EFYNs3JVcQg/cTX1rVCMOxGMkoqKP3LVsxbzWzxvdD/IdR6XLe9aJ3G7hMJ2pByxW815ZNT+OlQa0Qxw80fL2SqC7huDJiEeCS6RkZtW81mcJOalBEXzoEC8quJUCepvoFDWcR+8Ms8P9ek7Lp4kCr14hHHoZRX7CVTVM6DXCgi6NRSQcm7XSZFSezhRy4piVXDGIqbwnjRA/sJsx+cdrUcPm0g5hcw==")
    decoded_length = len(base64.b64decode("UQhCyTmwY0Lw8z7033bgt3ZKn3inT3NGKGow3UNxKg2Vzpd8uN1ZPf4EFYNs3JVcQg/cTX1rVCMOxGMkoqKP3LVsxbzWzxvdD/IdR6XLe9aJ3G7hMJ2pByxW815ZNT+OlQa0Qxw80fL2SqC7huDJiEeCS6RkZtW81mcJOalBEXzoEC8quJUCepvoFDWcR+8Ms8P9ek7Lp4kCr14hHHoZRX7CVTVM6DXCgi6NRSQcm7XSZFSezhRy4piVXDGIqbwnjRA/sJsx+cdrUcPm0g5hcw=="))
    
    print(f"\n📏 数据大小分析:")
    print(f"- Base64编码长度: {encrypted_length} 字符")
    print(f"- 解码后字节数: {decoded_length} 字节")
    print(f"- 估计原始JSON大小: {decoded_length - 16} ~ {decoded_length} 字节")

def main():
    """主函数"""
    
    # 分析加密数据
    result = analyze_encrypted_data()
    
    # 测试快递单号加密
    test_encrypt_tracking_number()
    
    # 分析数据结构
    analyze_data_structure()
    
    print("\n🎯 分析结论:")
    print("1. 这是一个AES-ECB加密的Base64编码数据")
    print("2. 数据长度表明这很可能是完整的请求JSON")
    print("3. 需要正确的密钥才能解密")
    print("4. 可能包含了所有请求参数的加密版本")

if __name__ == "__main__":
    main()
