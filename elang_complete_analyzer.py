import ida_funcs
import ida_name
import idautils
import idc
import ida_xref
import re
import time
from collections import defaultdict

class ELangCompleteAnalyzer:
    def __init__(self):
        self.results = {
            'is_elang': False,
            'support_libraries': {},
            'network_functions': [],
            'categorized_strings': {},
            'crypto_functions': [],
            'potential_keys': [],
            'file_operations': [],
            'registry_operations': []
        }
        
        # 易语言支持库前缀
        self.lib_prefixes = {
            'krnln_': '核心支持库',
            'shell_': '外壳支持库', 
            'spec_': '特殊功能支持库',
            'internet_': '网络支持库',
            'dp_': '数据操作支持库',
            'commobj_': '通用对象支持库',
            'eAPI_': '系统核心支持库',
            'RegEx_': '正则表达式支持库',
            'mysql_': 'MySQL数据库支持库',
            'sqlite_': 'SQLite数据库支持库'
        }
        
        # 网络相关API
        self.network_apis = [
            'InternetOpenA', 'InternetOpenW', 'InternetConnectA', 'InternetConnectW', 
            'HttpOpenRequestA', 'HttpOpenRequestW', 'HttpSendRequestA', 'HttpSendRequestW',
            'InternetReadFile', 'InternetWriteFile', 'URLDownloadToFileA', 'URLDownloadToFileW',
            'WinHttpOpen', 'WinHttpConnect', 'WinHttpOpenRequest', 'WinHttpSendRequest',
            'send', 'recv', 'WSAStartup', 'socket', 'connect', 'closesocket'
        ]
        
        # 加密相关API
        self.crypto_apis = [
            'CryptAcquireContextA', 'CryptAcquireContextW', 'CryptCreateHash', 'CryptHashData',
            'CryptEncrypt', 'CryptDecrypt', 'CryptGenKey', 'BCryptOpenAlgorithmProvider',
            'BCryptCreateHash', 'BCryptEncrypt', 'BCryptDecrypt'
        ]
        
        # 文件操作API
        self.file_apis = [
            'CreateFileA', 'CreateFileW', 'ReadFile', 'WriteFile', 'DeleteFileA', 'DeleteFileW',
            'CopyFileA', 'CopyFileW', 'MoveFileA', 'MoveFileW', 'FindFirstFileA', 'FindFirstFileW'
        ]
        
        # 注册表操作API
        self.registry_apis = [
            'RegOpenKeyA', 'RegOpenKeyW', 'RegQueryValueA', 'RegQueryValueW',
            'RegSetValueA', 'RegSetValueW', 'RegCreateKeyA', 'RegCreateKeyW', 'RegDeleteKeyA', 'RegDeleteKeyW'
        ]
        
        # 编码相关函数名模式
        self.encoding_patterns = [
            'base64', 'md5', 'sha1', 'sha256', 'aes', 'des', 'rc4',
            'encode', 'decode', 'encrypt', 'decrypt', 'hash'
        ]

    def check_elang_features(self):
        """1. 检查易语言程序特征"""
        print("[+] 检查易语言程序特征...")
        
        elang_indicators = []
        
        # 检查易语言运行时库特征
        elang_dlls = ['krnln.fnr', 'shell.fne', 'spec.fne', 'internet.fne']
        
        try:
            for module_name in idautils.Modules():
                module_name_lower = module_name.lower()
                for dll in elang_dlls:
                    if dll in module_name_lower:
                        elang_indicators.append(f"发现易语言支持库: {module_name}")
        except:
            pass
        
        # 检查函数名特征
        elang_func_patterns = ['krnln_', 'shell_', 'spec_', 'internet_']
        func_count = 0
        
        for func_ea in idautils.Functions():
            func_name = ida_name.get_name(func_ea)
            for pattern in elang_func_patterns:
                if pattern in func_name:
                    func_count += 1
                    break
        
        if func_count > 5:
            elang_indicators.append(f"发现 {func_count} 个疑似易语言函数")
        
        # 检查中文字符串
        chinese_strings = 0
        try:
            for string in idautils.Strings():
                string_val = str(string)
                if any('\u4e00' <= char <= '\u9fff' for char in string_val):
                    chinese_strings += 1
        except:
            pass
        
        if chinese_strings > 3:
            elang_indicators.append(f"发现 {chinese_strings} 个中文字符串")
        
        self.results['is_elang'] = len(elang_indicators) > 0
        
        print("易语言特征:")
        for indicator in elang_indicators:
            print(f"  ✓ {indicator}")
        
        return len(elang_indicators) > 0

    def analyze_support_libraries(self):
        """2. 分析易语言支持库函数"""
        print("\n[+] 分析易语言支持库函数...")
        
        support_lib_functions = {}
        
        for func_ea in idautils.Functions():
            func_name = ida_name.get_name(func_ea)
            
            for prefix, lib_name in self.lib_prefixes.items():
                if func_name.startswith(prefix):
                    if lib_name not in support_lib_functions:
                        support_lib_functions[lib_name] = []
                    
                    func_obj = ida_funcs.get_func(func_ea)
                    func_size = func_obj.size() if func_obj else 0
                    
                    support_lib_functions[lib_name].append({
                        'name': func_name,
                        'address': hex(func_ea),
                        'size': func_size
                    })
        
        self.results['support_libraries'] = support_lib_functions
        
        # 输出结果
        for lib_name, functions in support_lib_functions.items():
            print(f"\n{lib_name} ({len(functions)} 个函数):")
            for func in functions[:5]:  # 显示前5个
                print(f"  {func['name']} - {func['address']}")
            if len(functions) > 5:
                print(f"  ... 还有 {len(functions) - 5} 个函数")
        
        return support_lib_functions

    def analyze_network_functions(self):
        """3. 分析网络相关功能"""
        print("\n[+] 分析网络通信功能...")
        
        network_functions = []
        
        for api in self.network_apis:
            # 查找API
            api_ea = ida_name.get_name_ea(idc.BADADDR, api)
            if api_ea != idc.BADADDR:
                # 查找调用此API的函数
                for xref in idautils.XrefsTo(api_ea):
                    caller_func = ida_funcs.get_func(xref.frm)
                    if caller_func:
                        caller_name = ida_name.get_name(caller_func.start_ea)
                        
                        # 获取调用点附近的字符串
                        nearby_strings = self.get_nearby_strings(caller_func, xref.frm)
                        
                        network_functions.append({
                            'api': api,
                            'caller_function': caller_name,
                            'caller_address': hex(caller_func.start_ea),
                            'call_site': hex(xref.frm),
                            'nearby_strings': nearby_strings
                        })
        
        self.results['network_functions'] = network_functions
        
        # 输出结果
        if network_functions:
            print("发现的网络功能:")
            for func in network_functions:
                print(f"\nAPI: {func['api']}")
                print(f"调用函数: {func['caller_function']} ({func['caller_address']})")
                if func['nearby_strings']:
                    print("相关字符串:")
                    for string in func['nearby_strings'][:3]:
                        print(f"  - {string}")
        else:
            print("未发现明显的网络API调用")
        
        return network_functions

    def get_nearby_strings(self, func, call_site):
        """获取函数调用点附近的字符串"""
        nearby_strings = []
        
        try:
            ea = max(func.start_ea, call_site - 100)
            end_ea = min(func.end_ea, call_site + 100)
            
            while ea < end_ea:
                for ref in idautils.XrefsFrom(ea, 0):
                    if ref.type == ida_xref.dr_O:
                        string_val = idc.get_strlit_contents(ref.to)
                        if string_val:
                            try:
                                decoded = string_val.decode('utf-8', errors='ignore')
                                if len(decoded) > 3 and decoded not in nearby_strings:
                                    nearby_strings.append(decoded)
                            except:
                                pass
                ea = idc.next_head(ea)
        except:
            pass
        
        return nearby_strings[:5]  # 最多返回5个字符串

    def analyze_strings_smart(self):
        """4. 智能字符串分析"""
        print("\n[+] 智能字符串分析...")
        
        categorized_strings = {
            'urls': [],
            'file_paths': [],
            'registry_keys': [],
            'chinese_text': [],
            'api_endpoints': [],
            'credentials': [],
            'config_data': [],
            'network_codes': [],
            'employee_ids': [],
            'passwords': [],
            'device_sns': []
        }
        
        try:
            for string in idautils.Strings():
                string_val = str(string)
                string_addr = hex(string.ea)
                
                # 快递相关特殊模式
                if re.match(r'^53\d{4}$', string_val):  # 网点编号
                    categorized_strings['network_codes'].append({'value': string_val, 'address': string_addr})
                elif re.match(r'^\d{4}$', string_val):  # 4位工号
                    categorized_strings['employee_ids'].append({'value': string_val, 'address': string_addr})
                elif re.match(r'^88\d{4}$', string_val):  # 口令
                    categorized_strings['passwords'].append({'value': string_val, 'address': string_addr})
                elif re.match(r'^599\d{11}$', string_val):  # 设备SN
                    categorized_strings['device_sns'].append({'value': string_val, 'address': string_addr})
                
                # URL
                elif re.search(r'https?://', string_val, re.IGNORECASE):
                    categorized_strings['urls'].append({'value': string_val, 'address': string_addr})
                
                # 文件路径
                elif re.search(r'[A-Za-z]:\\|\\\\|/[a-zA-Z]', string_val):
                    categorized_strings['file_paths'].append({'value': string_val, 'address': string_addr})
                
                # 注册表键
                elif re.search(r'HKEY_|SOFTWARE\\|SYSTEM\\', string_val, re.IGNORECASE):
                    categorized_strings['registry_keys'].append({'value': string_val, 'address': string_addr})
                
                # 中文文本
                elif any('\u4e00' <= char <= '\u9fff' for char in string_val):
                    categorized_strings['chinese_text'].append({'value': string_val, 'address': string_addr})
                
                # API端点
                elif string_val.startswith('/') and len(string_val) > 3:
                    categorized_strings['api_endpoints'].append({'value': string_val, 'address': string_addr})
                
                # 可能的凭据
                elif re.search(r'(password|pwd|key|token|secret)', string_val, re.IGNORECASE):
                    categorized_strings['credentials'].append({'value': string_val, 'address': string_addr})
                
                # 配置数据
                elif re.search(r'[{}\[\]"]|<[^>]+>', string_val) and len(string_val) > 10:
                    categorized_strings['config_data'].append({'value': string_val, 'address': string_addr})
        except:
            pass
        
        self.results['categorized_strings'] = categorized_strings
        
        # 输出结果
        for category, strings in categorized_strings.items():
            if strings:
                print(f"\n{category.upper()} ({len(strings)} 个):")
                for item in strings[:5]:  # 显示前5个
                    print(f"  {item['value']} - {item['address']}")
                if len(strings) > 5:
                    print(f"  ... 还有 {len(strings) - 5} 个")
        
        return categorized_strings

    def analyze_crypto_functions(self):
        """5. 分析加密相关功能"""
        print("\n[+] 分析加密解密功能...")

        crypto_functions = []
        potential_keys = []

        # 查找加密API调用
        for api in self.crypto_apis:
            api_ea = ida_name.get_name_ea(idc.BADADDR, api)
            if api_ea != idc.BADADDR:
                for xref in idautils.XrefsTo(api_ea):
                    caller_func = ida_funcs.get_func(xref.frm)
                    if caller_func:
                        crypto_functions.append({
                            'type': 'crypto_api',
                            'api': api,
                            'function': ida_name.get_name(caller_func.start_ea),
                            'address': hex(caller_func.start_ea)
                        })

        # 查找可能的加密函数（通过函数名）
        for func_ea in idautils.Functions():
            func_name = ida_name.get_name(func_ea).lower()
            for pattern in self.encoding_patterns:
                if pattern in func_name:
                    crypto_functions.append({
                        'type': 'crypto_function',
                        'pattern': pattern,
                        'function': ida_name.get_name(func_ea),
                        'address': hex(func_ea)
                    })
                    break

        # 查找可能的密钥字符串
        try:
            for string in idautils.Strings():
                string_val = str(string)

                # 十六进制密钥
                if re.match(r'^[0-9a-fA-F]{16,}$', string_val):
                    potential_keys.append({
                        'type': 'hex_key',
                        'value': string_val,
                        'address': hex(string.ea)
                    })

                # Base64密钥
                elif re.match(r'^[A-Za-z0-9+/]{20,}={0,2}$', string_val):
                    potential_keys.append({
                        'type': 'base64_key',
                        'value': string_val,
                        'address': hex(string.ea)
                    })
        except:
            pass

        self.results['crypto_functions'] = crypto_functions
        self.results['potential_keys'] = potential_keys

        # 输出结果
        if crypto_functions:
            print("发现的加密功能:")
            for func in crypto_functions:
                if func['type'] == 'crypto_api':
                    print(f"  API调用: {func['api']} - 函数: {func['function']} ({func['address']})")
                else:
                    print(f"  可疑函数: {func['function']} ({func['address']}) - 模式: {func['pattern']}")

        if potential_keys:
            print(f"\n发现 {len(potential_keys)} 个可能的密钥:")
            for key in potential_keys[:5]:
                print(f"  {key['type']}: {key['value'][:50]}... - {key['address']}")

        return crypto_functions, potential_keys

    def analyze_file_operations(self):
        """6. 分析文件操作功能"""
        print("\n[+] 分析文件操作功能...")

        file_operations = []

        for api in self.file_apis:
            api_ea = ida_name.get_name_ea(idc.BADADDR, api)
            if api_ea != idc.BADADDR:
                for xref in idautils.XrefsTo(api_ea):
                    caller_func = ida_funcs.get_func(xref.frm)
                    if caller_func:
                        caller_name = ida_name.get_name(caller_func.start_ea)
                        nearby_strings = self.get_nearby_strings(caller_func, xref.frm)

                        file_operations.append({
                            'api': api,
                            'caller_function': caller_name,
                            'caller_address': hex(caller_func.start_ea),
                            'call_site': hex(xref.frm),
                            'nearby_strings': nearby_strings
                        })

        self.results['file_operations'] = file_operations

        # 输出结果
        if file_operations:
            print("发现的文件操作:")
            for op in file_operations:
                print(f"\nAPI: {op['api']}")
                print(f"调用函数: {op['caller_function']} ({op['caller_address']})")
                if op['nearby_strings']:
                    print("相关文件路径:")
                    for string in op['nearby_strings'][:3]:
                        if any(char in string for char in ['\\', '/', '.']):
                            print(f"  - {string}")
        else:
            print("未发现明显的文件操作API调用")

        return file_operations

    def analyze_registry_operations(self):
        """7. 分析注册表操作功能"""
        print("\n[+] 分析注册表操作功能...")

        registry_operations = []

        for api in self.registry_apis:
            api_ea = ida_name.get_name_ea(idc.BADADDR, api)
            if api_ea != idc.BADADDR:
                for xref in idautils.XrefsTo(api_ea):
                    caller_func = ida_funcs.get_func(xref.frm)
                    if caller_func:
                        caller_name = ida_name.get_name(caller_func.start_ea)
                        nearby_strings = self.get_nearby_strings(caller_func, xref.frm)

                        registry_operations.append({
                            'api': api,
                            'caller_function': caller_name,
                            'caller_address': hex(caller_func.start_ea),
                            'call_site': hex(xref.frm),
                            'nearby_strings': nearby_strings
                        })

        self.results['registry_operations'] = registry_operations

        # 输出结果
        if registry_operations:
            print("发现的注册表操作:")
            for op in registry_operations:
                print(f"\nAPI: {op['api']}")
                print(f"调用函数: {op['caller_function']} ({op['caller_address']})")
                if op['nearby_strings']:
                    print("相关注册表键:")
                    for string in op['nearby_strings'][:3]:
                        if any(key in string.upper() for key in ['HKEY', 'SOFTWARE', 'SYSTEM']):
                            print(f"  - {string}")
        else:
            print("未发现明显的注册表操作API调用")

        return registry_operations

    def run_complete_analysis(self):
        """运行完整分析"""
        print("=" * 80)
        print("易语言程序完整分析工具")
        print("=" * 80)

        try:
            filename = ida_name.get_root_filename()
        except:
            filename = "Unknown"

        print(f"文件名: {filename}")
        print(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("")

        # 执行所有分析
        self.check_elang_features()
        self.analyze_support_libraries()
        self.analyze_network_functions()
        self.analyze_strings_smart()
        self.analyze_crypto_functions()
        self.analyze_file_operations()
        self.analyze_registry_operations()

        # 生成摘要报告
        self.generate_summary_report()

        # 保存详细报告
        self.save_detailed_report()

    def generate_summary_report(self):
        """生成摘要报告"""
        print("\n" + "=" * 80)
        print("分析摘要报告")
        print("=" * 80)

        # 基本信息
        print(f"程序类型: {'易语言程序' if self.results['is_elang'] else '未知类型'}")
        print(f"支持库数量: {len(self.results['support_libraries'])}")
        print(f"网络功能: {len(self.results['network_functions'])} 个API调用")
        print(f"加密功能: {len(self.results['crypto_functions'])} 个")
        print(f"文件操作: {len(self.results['file_operations'])} 个API调用")
        print(f"注册表操作: {len(self.results['registry_operations'])} 个API调用")

        # 字符串统计
        total_strings = sum(len(strings) for strings in self.results['categorized_strings'].values())
        print(f"字符串总数: {total_strings}")

        # 关键发现
        print("\n【关键发现】")

        # 快递相关信息
        express_info = []
        if self.results['categorized_strings']['network_codes']:
            express_info.append(f"网点编号: {len(self.results['categorized_strings']['network_codes'])} 个")
        if self.results['categorized_strings']['employee_ids']:
            express_info.append(f"工号: {len(self.results['categorized_strings']['employee_ids'])} 个")
        if self.results['categorized_strings']['passwords']:
            express_info.append(f"口令: {len(self.results['categorized_strings']['passwords'])} 个")
        if self.results['categorized_strings']['device_sns']:
            express_info.append(f"设备SN: {len(self.results['categorized_strings']['device_sns'])} 个")

        if express_info:
            print("快递系统认证信息:")
            for info in express_info:
                print(f"  ✓ {info}")

        # 网络通信
        if self.results['categorized_strings']['urls']:
            print("网络通信:")
            for url in self.results['categorized_strings']['urls'][:3]:
                print(f"  ✓ URL: {url['value']}")

        # 加密密钥
        if self.results['potential_keys']:
            print("发现的密钥:")
            for key in self.results['potential_keys'][:3]:
                print(f"  ✓ {key['type']}: {key['value'][:30]}...")

        print("\n" + "=" * 80)

    def save_detailed_report(self):
        """保存详细报告到文件"""
        try:
            filename = ida_name.get_root_filename()
        except:
            filename = "unknown"

        report_filename = f"elang_complete_analysis_{filename}_{int(time.time())}.txt"

        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("易语言程序完整分析报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"文件名: {filename}\n")
                f.write(f"分析时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # 1. 程序特征
                f.write("1. 程序特征\n")
                f.write("-" * 30 + "\n")
                f.write(f"易语言程序: {'是' if self.results['is_elang'] else '否'}\n\n")

                # 2. 支持库函数
                f.write("2. 支持库函数\n")
                f.write("-" * 30 + "\n")
                for lib_name, functions in self.results['support_libraries'].items():
                    f.write(f"\n{lib_name} ({len(functions)} 个函数):\n")
                    for func in functions:
                        f.write(f"  {func['name']} - {func['address']}\n")
                f.write("\n")

                # 3. 网络功能
                f.write("3. 网络功能\n")
                f.write("-" * 30 + "\n")
                for func in self.results['network_functions']:
                    f.write(f"API: {func['api']}\n")
                    f.write(f"调用函数: {func['caller_function']} ({func['caller_address']})\n")
                    f.write(f"调用位置: {func['call_site']}\n")
                    if func['nearby_strings']:
                        f.write("相关字符串:\n")
                        for string in func['nearby_strings']:
                            f.write(f"  - {string}\n")
                    f.write("\n")

                # 4. 字符串分类
                f.write("4. 字符串分类\n")
                f.write("-" * 30 + "\n")
                for category, strings in self.results['categorized_strings'].items():
                    if strings:
                        f.write(f"\n{category.upper()} ({len(strings)} 个):\n")
                        for item in strings:
                            f.write(f"  {item['value']} - {item['address']}\n")
                f.write("\n")

                # 5. 加密功能
                f.write("5. 加密功能\n")
                f.write("-" * 30 + "\n")
                for func in self.results['crypto_functions']:
                    if func['type'] == 'crypto_api':
                        f.write(f"API调用: {func['api']} - 函数: {func['function']} ({func['address']})\n")
                    else:
                        f.write(f"可疑函数: {func['function']} ({func['address']}) - 模式: {func['pattern']}\n")

                if self.results['potential_keys']:
                    f.write(f"\n发现的密钥 ({len(self.results['potential_keys'])} 个):\n")
                    for key in self.results['potential_keys']:
                        f.write(f"  {key['type']}: {key['value']} - {key['address']}\n")
                f.write("\n")

                # 6. 文件操作
                f.write("6. 文件操作\n")
                f.write("-" * 30 + "\n")
                for op in self.results['file_operations']:
                    f.write(f"API: {op['api']}\n")
                    f.write(f"调用函数: {op['caller_function']} ({op['caller_address']})\n")
                    if op['nearby_strings']:
                        f.write("相关文件路径:\n")
                        for string in op['nearby_strings']:
                            if any(char in string for char in ['\\', '/', '.']):
                                f.write(f"  - {string}\n")
                    f.write("\n")

                # 7. 注册表操作
                f.write("7. 注册表操作\n")
                f.write("-" * 30 + "\n")
                for op in self.results['registry_operations']:
                    f.write(f"API: {op['api']}\n")
                    f.write(f"调用函数: {op['caller_function']} ({op['caller_address']})\n")
                    if op['nearby_strings']:
                        f.write("相关注册表键:\n")
                        for string in op['nearby_strings']:
                            if any(key in string.upper() for key in ['HKEY', 'SOFTWARE', 'SYSTEM']):
                                f.write(f"  - {string}\n")
                    f.write("\n")

            print(f"\n[+] 详细报告已保存到: {report_filename}")

        except Exception as e:
            print(f"[-] 保存报告失败: {e}")

def main():
    """主函数"""
    print("开始易语言程序完整分析...")

    analyzer = ELangCompleteAnalyzer()
    analyzer.run_complete_analysis()

    print("\n分析完成!")

# 在IDA中运行
if __name__ == "__main__":
    main()
