﻿#Software: 韵达自动化扫描客户端
#ProcessID: 13060 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,573M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:18:18.799  5 Y P KuangScannerNet 准备开始监听4个服务器
06:18:18.810  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:18:18.815  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:18:18.819  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:18:18.900  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:18:18.900  5 Y P KuangScannerNet 准备就绪！
06:18:18.917  5 Y P Http 准备开始监听2个服务器
06:18:18.939  5 Y P Http 开始监听 tcp://0.0.0.0:8080
06:18:18.957  5 Y P Http 开始监听 tcp://[::]:8080
06:18:18.957  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 11972 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,902M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:27:18.373  5 Y P KuangScannerNet 准备开始监听4个服务器
06:27:18.465  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:27:18.468  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:27:18.478  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:27:18.482  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:27:18.482  5 Y P KuangScannerNet 准备就绪！
06:27:18.494  5 Y P Http 准备开始监听2个服务器
06:27:18.499  5 Y P Http 开始监听 tcp://0.0.0.0:8080
06:27:18.515  5 Y P Http 开始监听 tcp://[::]:8080
06:27:18.515  5 Y P Http 准备就绪！
06:31:11.336  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
06:31:11.336  1 N - NewLife组件核心库 ©2002-2022 NewLife
06:31:11.336  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
06:31:11.336  1 N - 韵达自动化扫描客户端 Copyright ©  2022
06:31:11.340  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
06:31:11.340  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 14928 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,533M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:40:47.932  7 Y P KuangScannerNet 准备开始监听4个服务器
06:40:47.963  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:40:47.979  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:40:47.983  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:40:47.988  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:40:47.989  7 Y P KuangScannerNet 准备就绪！
06:40:48.003  7 Y P Http 准备开始监听2个服务器
06:40:48.044  7 Y P Http 开始监听 tcp://0.0.0.0:8080
06:40:48.060  7 Y P Http 开始监听 tcp://[::]:8080
06:40:48.060  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 14776 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,519M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-02-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
08:28:04.924  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
08:28:04.926  1 N - NewLife组件核心库 ©2002-2022 NewLife
08:28:04.926  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
08:28:04.926  1 N - 韵达自动化扫描客户端 Copyright ©  2022
08:28:04.926  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
08:28:04.937  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 18172 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,765M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:37:08.867 15 Y P KuangScannerNet 准备开始监听4个服务器
13:37:08.981 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
13:37:08.989 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
13:37:08.994 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
13:37:09.001 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
13:37:09.001 15 Y P KuangScannerNet 准备就绪！
13:37:09.017 15 Y P Http 准备开始监听2个服务器
13:37:09.018 15 Y P Http 开始监听 tcp://0.0.0.0:8080
13:37:09.032 15 Y P Http 开始监听 tcp://[::]:8080
13:37:09.032 15 Y P Http 准备就绪！
