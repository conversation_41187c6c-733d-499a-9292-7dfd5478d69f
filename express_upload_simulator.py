#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快递单号上传脚本模拟器
基于IDA伪代码分析生成的Python实现

主要功能：
1. 设备认证登录
2. 快递单号数据加密上传
3. 网络请求处理

关键信息：
- 网点编号：530023
- 业务员工号：9528  
- 业务员口令：881000
- 分拨中心代码：530001
- 巴枪SN码：59992231000470
"""

import requests
import json
import time
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import uuid

class ExpressUploader:
    def __init__(self):
        # 服务器配置
        self.base_url = "http://scan.yundasys.com:9900/rock"
        self.upload_url = f"{self.base_url}/upload/outlet/scan/bq/v1"
        self.login_url = f"{self.base_url}/query/wdBqLogin/v1"
        
        # 设备认证信息
        self.network_code = "530023"      # 网点编号
        self.employee_id = "9528"         # 业务员4位工号
        self.password = "881000"          # 业务员口令
        self.center_code = "530001"       # 上级分拨中心代码
        self.device_sn = "59992231000470" # 巴枪SN码
        
        # 加密配置
        self.aes_key = "k301qsjbrh1s6ega"  # AES密钥
        self.version = "3.6.8.0821"       # 版本号
        self.app_type = "wddpyt"           # 应用类型
        
        # 会话信息
        self.token = None
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            "User-Agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
            "Content-Type": "application/octet-stream"
        }

    def generate_device_info(self):
        """生成设备信息JSON"""
        device_info = {
            "imei": self.device_sn,      # 使用设备SN作为IMEI
            "mac": "00:11:22:33:44:55", # MAC地址
            "serialNumber": self.device_sn
        }
        return json.dumps(device_info, separators=(',', ':'))

    def generate_timestamp(self):
        """生成时间戳"""
        return str(int(time.time() * 1000))

    def calculate_digest(self, data):
        """计算数据摘要"""
        # 使用MD5计算摘要
        md5_hash = hashlib.md5()
        md5_hash.update(data.encode('utf-8'))
        return md5_hash.hexdigest()

    def aes_encrypt(self, plaintext):
        """AES-ECB加密"""
        try:
            key = self.aes_key.encode('utf-8')
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 填充数据到16字节的倍数
            padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_data)
            
            # 返回Base64编码的结果
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"AES加密失败: {e}")
            return None

    def aes_decrypt(self, ciphertext):
        """AES-ECB解密"""
        try:
            key = self.aes_key.encode('utf-8')
            cipher = AES.new(key, AES.MODE_ECB)
            
            # Base64解码
            encrypted_data = base64.b64decode(ciphertext)
            
            # 解密
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            plaintext = unpad(decrypted, AES.block_size)
            
            return plaintext.decode('utf-8')
        except Exception as e:
            print(f"AES解密失败: {e}")
            return None

    def login(self):
        """设备登录认证"""
        print("[+] 开始设备登录认证...")
        
        # 生成登录请求数据
        req_time = self.generate_timestamp()
        device_info = self.generate_device_info()
        
        # 构建登录JSON
        login_data = {
            "appType": "1",
            "company": self.network_code,
            "device": device_info,
            "digest": "",  # 稍后计算
            "password": self.password,
            "reqTime": req_time,
            "uniqueCode": str(uuid.uuid4()),
            "user": self.employee_id,
            "version": self.version
        }
        
        # 计算摘要
        data_str = json.dumps(login_data, separators=(',', ':'))
        login_data["digest"] = self.calculate_digest(data_str)
        
        try:
            # 发送登录请求
            response = self.session.post(
                self.login_url,
                json=login_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.token = result.get("data", {}).get("token")
                    print(f"[+] 登录成功，获取到token: {self.token[:20]}...")
                    return True
                else:
                    print(f"[-] 登录失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"[-] 登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[-] 登录异常: {e}")
            return False

    def upload_tracking_number(self, tracking_number):
        """上传快递单号"""
        if not self.token:
            print("[-] 未登录，请先执行登录")
            return False
            
        print(f"[+] 开始上传快递单号: {tracking_number}")
        
        try:
            # 构建上传数据
            upload_data = {
                "trackingNumber": tracking_number,
                "networkCode": self.network_code,
                "employeeId": self.employee_id,
                "centerCode": self.center_code,
                "deviceSN": self.device_sn,
                "timestamp": self.generate_timestamp(),
                "version": self.version,
                "appType": self.app_type
            }
            
            # 转换为JSON字符串
            json_data = json.dumps(upload_data, separators=(',', ':'))
            print(f"[+] 原始数据: {json_data}")
            
            # AES加密
            encrypted_data = self.aes_encrypt(json_data)
            if not encrypted_data:
                print("[-] 数据加密失败")
                return False
                
            print(f"[+] 加密后数据: {encrypted_data[:50]}...")
            
            # 设置请求头，包含token
            upload_headers = self.headers.copy()
            upload_headers["token"] = self.token
            
            # 发送上传请求
            response = self.session.post(
                self.upload_url,
                data=encrypted_data,
                headers=upload_headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"[+] 上传成功，响应: {response.text}")
                return True
            else:
                print(f"[-] 上传失败，状态码: {response.status_code}")
                print(f"[-] 响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"[-] 上传异常: {e}")
            return False

    def batch_upload(self, tracking_numbers):
        """批量上传快递单号"""
        print(f"[+] 开始批量上传 {len(tracking_numbers)} 个快递单号")
        
        success_count = 0
        for i, tracking_number in enumerate(tracking_numbers, 1):
            print(f"\n[{i}/{len(tracking_numbers)}] 处理: {tracking_number}")
            
            if self.upload_tracking_number(tracking_number):
                success_count += 1
                print(f"[+] 成功")
            else:
                print(f"[-] 失败")
            
            # 避免请求过快
            time.sleep(1)
        
        print(f"\n[+] 批量上传完成，成功: {success_count}/{len(tracking_numbers)}")
        return success_count

def main():
    """主函数"""
    print("快递单号上传脚本模拟器")
    print("=" * 50)
    
    # 创建上传器实例
    uploader = ExpressUploader()
    
    # 显示配置信息
    print("配置信息:")
    print(f"  网点编号: {uploader.network_code}")
    print(f"  业务员工号: {uploader.employee_id}")
    print(f"  业务员口令: {uploader.password}")
    print(f"  分拨中心代码: {uploader.center_code}")
    print(f"  设备SN: {uploader.device_sn}")
    print(f"  服务器地址: {uploader.base_url}")
    print()
    
    # 执行登录
    if not uploader.login():
        print("登录失败，程序退出")
        return
    
    # 测试单个快递单号上传
    test_tracking_number = "YT1234567890123"
    uploader.upload_tracking_number(test_tracking_number)
    
    # 测试批量上传
    test_tracking_numbers = [
        "YT1234567890124",
        "YT1234567890125", 
        "YT1234567890126"
    ]
    uploader.batch_upload(test_tracking_numbers)

if __name__ == "__main__":
    main()
