#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快递单号上传脚本模拟器
基于IDA伪代码分析生成的Python实现

主要功能：
1. 设备认证登录
2. 快递单号数据加密上传
3. 网络请求处理

关键信息：
- 网点编号：530023
- 业务员工号：9528  
- 业务员口令：881000
- 分拨中心代码：530001
- 巴枪SN码：59992231000470
"""

import requests
import json
import time
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import uuid

class ExpressUploader:
    def __init__(self):
        # 服务器配置
        self.base_url = "http://scan.yundasys.com:9900/rock"
        self.upload_url = f"{self.base_url}/upload/outlet/scan/bq/v1"
        self.login_url = f"{self.base_url}/query/wdBqLogin/v1"

        # 设备认证信息
        self.network_code = "530023"      # 网点编号
        self.employee_id = "9528"         # 业务员4位工号
        self.password = "881000"          # 业务员口令
        self.center_code = "530001"       # 上级分拨中心代码
        self.device_sn = "59992231000470" # 巴枪SN码

        # 加密配置
        self.aes_key = "k301qsjbrh1s6ega"  # AES密钥
        self.version = "3.6.8.0821"       # 版本号

        # 快递操作类型配置
        self.operation_types = {
            "arrival": {      # 到达
                "app_type": "XCRK",
                "operation_code": 24,
                "packet_type": "2(0)"
            },
            "package": {      # 建包
                "app_type": "wddpyt",
                "operation_code": 13,
                "packet_type": "2(1)"
            },
            "town": {         # 乡镇
                "app_type": "wddpyt",
                "operation_code": 13,
                "packet_type": "2(1)"
            },
            "delivery": {     # 到派
                "app_type": "wddpyt",
                "operation_code": 13,
                "packet_type": "2(1)"
            }
        }

        # 会话信息
        self.token = None
        self.session = requests.Session()

        # 设置请求头
        self.headers = {
            "User-Agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
            "Content-Type": "application/octet-stream"
        }

    def generate_device_info(self):
        """生成设备信息JSON"""
        device_info = {
            "imei": self.device_sn,      # 使用设备SN作为IMEI
            "mac": "00:11:22:33:44:55", # MAC地址
            "serialNumber": self.device_sn
        }
        return json.dumps(device_info, separators=(',', ':'))

    def generate_timestamp(self):
        """生成时间戳"""
        return str(int(time.time() * 1000))

    def calculate_digest(self, data):
        """计算数据摘要"""
        # 使用MD5计算摘要
        md5_hash = hashlib.md5()
        md5_hash.update(data.encode('utf-8'))
        return md5_hash.hexdigest()

    def aes_encrypt(self, plaintext):
        """AES-ECB加密"""
        try:
            key = self.aes_key.encode('utf-8')
            cipher = AES.new(key, AES.MODE_ECB)
            
            # 填充数据到16字节的倍数
            padded_data = pad(plaintext.encode('utf-8'), AES.block_size)
            
            # 加密
            encrypted = cipher.encrypt(padded_data)
            
            # 返回Base64编码的结果
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"AES加密失败: {e}")
            return None

    def aes_decrypt(self, ciphertext):
        """AES-ECB解密"""
        try:
            key = self.aes_key.encode('utf-8')
            cipher = AES.new(key, AES.MODE_ECB)
            
            # Base64解码
            encrypted_data = base64.b64decode(ciphertext)
            
            # 解密
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            plaintext = unpad(decrypted, AES.block_size)
            
            return plaintext.decode('utf-8')
        except Exception as e:
            print(f"AES解密失败: {e}")
            return None

    def login(self):
        """设备登录认证"""
        print("[+] 开始设备登录认证...")
        
        # 生成登录请求数据
        req_time = self.generate_timestamp()
        device_info = self.generate_device_info()
        
        # 构建登录JSON
        login_data = {
            "appType": "1",
            "company": self.network_code,
            "device": device_info,
            "digest": "",  # 稍后计算
            "password": self.password,
            "reqTime": req_time,
            "uniqueCode": str(uuid.uuid4()),
            "user": self.employee_id,
            "version": self.version
        }
        
        # 计算摘要
        data_str = json.dumps(login_data, separators=(',', ':'))
        login_data["digest"] = self.calculate_digest(data_str)
        
        try:
            # 发送登录请求
            response = self.session.post(
                self.login_url,
                json=login_data,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.token = result.get("data", {}).get("token")
                    print(f"[+] 登录成功，获取到token: {self.token[:20]}...")
                    return True
                else:
                    print(f"[-] 登录失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"[-] 登录请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[-] 登录异常: {e}")
            return False

    def upload_tracking_number(self, tracking_number, operation_type="arrival"):
        """上传快递单号

        Args:
            tracking_number: 快递单号
            operation_type: 操作类型 ("arrival", "package", "town", "delivery")
        """
        if not self.token:
            print("[-] 未登录，请先执行登录")
            return False

        if operation_type not in self.operation_types:
            print(f"[-] 不支持的操作类型: {operation_type}")
            print(f"[-] 支持的类型: {list(self.operation_types.keys())}")
            return False

        op_config = self.operation_types[operation_type]
        print(f"[+] 开始上传快递单号: {tracking_number} (操作类型: {operation_type})")

        try:
            # 构建基础数据包结构
            packet_data = self._build_packet_data(tracking_number, op_config)

            # 转换为JSON字符串
            json_data = json.dumps(packet_data, separators=(',', ':'))
            print(f"[+] 原始数据包: {json_data[:100]}...")

            # AES加密
            encrypted_data = self.aes_encrypt(json_data)
            if not encrypted_data:
                print("[-] 数据加密失败")
                return False

            print(f"[+] 加密后数据: {encrypted_data[:50]}...")

            # 设置请求头，包含token
            upload_headers = self.headers.copy()
            upload_headers["token"] = self.token

            # 发送上传请求
            response = self.session.post(
                self.upload_url,
                data=encrypted_data,
                headers=upload_headers,
                timeout=30
            )

            if response.status_code == 200:
                print(f"[+] 上传成功，响应: {response.text}")
                return True
            else:
                print(f"[-] 上传失败，状态码: {response.status_code}")
                print(f"[-] 响应内容: {response.text}")
                return False

        except Exception as e:
            print(f"[-] 上传异常: {e}")
            return False

    def _build_packet_data(self, tracking_number, op_config):
        """构建数据包结构

        基于IDA分析的伪代码，构建符合协议的数据包
        """
        packet_type = op_config["packet_type"]
        app_type = op_config["app_type"]
        operation_code = op_config["operation_code"]

        # 基础数据包结构
        packet = {}

        # 根据数据包类型构建不同的字段
        if packet_type == "2(0)":  # 到达类型
            packet.update({
                f"{packet_type}.1": "0",
                f"{packet_type}.3": "0",
                f"{packet_type}.4": tracking_number,
                f"{packet_type}.9": operation_code,
                f"{packet_type}.11": "0.0",
                f"{packet_type}.17": "0",
                f"{packet_type}.18": "0",
                f"{packet_type}.22": "1",
                f"{packet_type}.23": "1",
                f"{packet_type}.25": "z20",
                f"{packet_type}.27": "0",
                f"{packet_type}.29": "0",
                f"{packet_type}.31": "",
                f"{packet_type}.31.11": 89,
                f"{packet_type}.32": self.employee_id,
                f"{packet_type}.39": "0",
                f"{packet_type}.41": tracking_number,
                f"{packet_type}.42": "0",
                f"{packet_type}.43": "0",
                f"{packet_type}.44": tracking_number,
                f"{packet_type}.46": operation_code,
                f"{packet_type}.47": self.generate_timestamp(),
                f"{packet_type}.54": app_type,
                f"{packet_type}.59": "0",
                f"{packet_type}.60": "0",
                f"{packet_type}.61": "0"
            })
        elif packet_type == "2(1)":  # 建包/乡镇/到派类型
            packet.update({
                f"{packet_type}.1": "0",
                f"{packet_type}.3": "0",
                f"{packet_type}.4": tracking_number,
                f"{packet_type}.11": "0.0",
                f"{packet_type}.17": "0",
                f"{packet_type}.18": "0",
                f"{packet_type}.22": "1",
                f"{packet_type}.23": "1",
                f"{packet_type}.27": "0",
                f"{packet_type}.29": "0",
                f"{packet_type}.32": self.employee_id,
                f"{packet_type}.39": "0",
                f"{packet_type}.42": "0",
                f"{packet_type}.43": "0",
                f"{packet_type}.44": tracking_number,
                f"{packet_type}.46": operation_code,
                f"{packet_type}.47": self.generate_timestamp(),
                f"{packet_type}.54": app_type,
                f"{packet_type}.59": "0",
                f"{packet_type}.60": "0",
                f"{packet_type}.61": "0"
            })

        return packet

    def batch_upload(self, tracking_numbers, operation_type="arrival"):
        """批量上传快递单号

        Args:
            tracking_numbers: 快递单号列表
            operation_type: 操作类型 ("arrival", "package", "town", "delivery")
        """
        print(f"[+] 开始批量上传 {len(tracking_numbers)} 个快递单号 (操作类型: {operation_type})")

        success_count = 0
        for i, tracking_number in enumerate(tracking_numbers, 1):
            print(f"\n[{i}/{len(tracking_numbers)}] 处理: {tracking_number}")

            if self.upload_tracking_number(tracking_number, operation_type):
                success_count += 1
                print(f"[+] 成功")
            else:
                print(f"[-] 失败")

            # 避免请求过快
            time.sleep(1)

        print(f"\n[+] 批量上传完成，成功: {success_count}/{len(tracking_numbers)}")
        return success_count

    def upload_by_operation_type(self, tracking_numbers, operation_types):
        """按不同操作类型上传快递单号

        Args:
            tracking_numbers: 快递单号列表
            operation_types: 对应的操作类型列表
        """
        if len(tracking_numbers) != len(operation_types):
            print("[-] 快递单号数量与操作类型数量不匹配")
            return 0

        print(f"[+] 开始混合类型上传 {len(tracking_numbers)} 个快递单号")

        success_count = 0
        for i, (tracking_number, op_type) in enumerate(zip(tracking_numbers, operation_types), 1):
            print(f"\n[{i}/{len(tracking_numbers)}] 处理: {tracking_number} ({op_type})")

            if self.upload_tracking_number(tracking_number, op_type):
                success_count += 1
                print(f"[+] 成功")
            else:
                print(f"[-] 失败")

            # 避免请求过快
            time.sleep(1)

        print(f"\n[+] 混合类型上传完成，成功: {success_count}/{len(tracking_numbers)}")
        return success_count

def main():
    """主函数"""
    print("快递单号上传脚本模拟器 - 支持四种操作类型")
    print("=" * 60)

    # 创建上传器实例
    uploader = ExpressUploader()

    # 显示配置信息
    print("配置信息:")
    print(f"  网点编号: {uploader.network_code}")
    print(f"  业务员工号: {uploader.employee_id}")
    print(f"  业务员口令: {uploader.password}")
    print(f"  分拨中心代码: {uploader.center_code}")
    print(f"  设备SN: {uploader.device_sn}")
    print(f"  服务器地址: {uploader.base_url}")
    print()

    # 显示支持的操作类型
    print("支持的操作类型:")
    for op_type, config in uploader.operation_types.items():
        print(f"  {op_type}: {config['app_type']} (操作码: {config['operation_code']})")
    print()

    # 执行登录
    if not uploader.login():
        print("登录失败，程序退出")
        return

    # 测试不同操作类型的单个快递单号上传
    test_tracking_number = "YT1234567890123"

    print("\n" + "=" * 60)
    print("测试不同操作类型的单号上传")
    print("=" * 60)

    for operation_type in uploader.operation_types.keys():
        print(f"\n--- 测试 {operation_type} 操作 ---")
        uploader.upload_tracking_number(test_tracking_number, operation_type)
        time.sleep(2)  # 避免请求过快

    # 测试批量上传（同一操作类型）
    print("\n" + "=" * 60)
    print("测试批量上传（到达操作）")
    print("=" * 60)

    test_tracking_numbers = [
        "YT1234567890124",
        "YT1234567890125",
        "YT1234567890126"
    ]
    uploader.batch_upload(test_tracking_numbers, "arrival")

    # 测试混合操作类型上传
    print("\n" + "=" * 60)
    print("测试混合操作类型上传")
    print("=" * 60)

    mixed_tracking_numbers = [
        "YT1234567890127",
        "YT1234567890128",
        "YT1234567890129",
        "YT1234567890130"
    ]
    mixed_operation_types = ["arrival", "package", "town", "delivery"]

    uploader.upload_by_operation_type(mixed_tracking_numbers, mixed_operation_types)

if __name__ == "__main__":
    main()
