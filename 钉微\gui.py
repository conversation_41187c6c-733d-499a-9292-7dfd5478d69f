import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import json
import threading
import logging
import queue
import time
import os

logger = logging.getLogger(__name__)

class LogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI"""
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
    
    def emit(self, record):
        log_entry = self.format(record)
        self.log_queue.put(log_entry)

class MessengerGUI:
    """消息转发工具的GUI界面"""
    
    def __init__(self, root, start_callback, stop_callback, config_file='config.json'):
        """初始化GUI界面"""
        self.root = root
        self.root.title("消息转发工具")
        self.root.geometry("800x600")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.start_callback = start_callback
        self.stop_callback = stop_callback
        self.config_file = config_file
        self.log_queue = queue.Queue()
        self.setup_logging()
        
        self.running = False
        self.worker_thread = None
        
        self.create_widgets()
        self.load_config()
        
        # 启动日志更新线程
        self.root.after(100, self.update_log)
    
    def setup_logging(self):
        """设置日志处理"""
        log_handler = LogHandler(self.log_queue)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)
        
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建标签页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 配置标签页
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="配置")
        self.create_config_widgets()
        
        # 日志标签页
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="日志")
        self.create_log_widgets()
        
        # 状态标签页
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text="状态")
        self.create_status_widgets()
        
        # 底部控制按钮
        self.control_frame = ttk.Frame(self.root)
        self.control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(self.control_frame, text="启动", command=self.start)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(self.control_frame, text="停止", command=self.stop, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(self.control_frame, text="保存配置", command=self.save_config)
        self.save_button.pack(side=tk.RIGHT, padx=5)
    
    def create_config_widgets(self):
        """创建配置页面的组件"""
        # 创建配置表单
        form_frame = ttk.LabelFrame(self.config_frame, text="基本配置")
        form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 钉钉群名称
        ttk.Label(form_frame, text="钉钉群名称:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.dingtalk_group_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.dingtalk_group_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 微信群名称
        ttk.Label(form_frame, text="微信群名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.wechat_group_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.wechat_group_var, width=40).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 检查间隔
        ttk.Label(form_frame, text="检查间隔(秒):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.check_interval_var = tk.IntVar()
        ttk.Spinbox(form_frame, from_=1, to=60, textvariable=self.check_interval_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 转发选项
        options_frame = ttk.LabelFrame(self.config_frame, text="转发选项")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.forward_all_dingtalk_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="转发所有钉钉消息", variable=self.forward_all_dingtalk_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        self.forward_filtered_wechat_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="转发筛选后的微信消息", variable=self.forward_filtered_wechat_var).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 关键词配置
        keywords_frame = ttk.LabelFrame(self.config_frame, text="关键词配置")
        keywords_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(keywords_frame, text="关键词(逗号分隔):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.keywords_var = tk.StringVar()
        ttk.Entry(keywords_frame, textvariable=self.keywords_var, width=60).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(keywords_frame, text="负面关键词(逗号分隔):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.negative_keywords_var = tk.StringVar()
        ttk.Entry(keywords_frame, textvariable=self.negative_keywords_var, width=60).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
    
    def create_log_widgets(self):
        """创建日志页面的组件"""
        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(self.log_frame, wrap=tk.WORD, width=80, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.log_text.config(state=tk.DISABLED)
        
        # 日志控制按钮
        control_frame = ttk.Frame(self.log_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.clear_log_button = ttk.Button(control_frame, text="清空日志", command=self.clear_log)
        self.clear_log_button.pack(side=tk.LEFT, padx=5)
        
        self.save_log_button = ttk.Button(control_frame, text="保存日志", command=self.save_log)
        self.save_log_button.pack(side=tk.LEFT, padx=5)
    
    def create_status_widgets(self):
        """创建状态页面的组件"""
        # 状态显示区域
        self.status_text = scrolledtext.ScrolledText(self.status_frame, wrap=tk.WORD, width=80, height=20)
        self.status_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.status_text.config(state=tk.DISABLED)
        
        # 状态更新按钮
        self.update_status_button = ttk.Button(self.status_frame, text="刷新状态", command=self.update_status_display)
        self.update_status_button.pack(side=tk.LEFT, padx=10, pady=5)
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.dingtalk_group_var.set(config.get('dingtalk_group_name', ''))
                self.wechat_group_var.set(config.get('wechat_group_name', ''))
                self.check_interval_var.set(config.get('check_interval', 5))
                self.forward_all_dingtalk_var.set(config.get('forward_all_dingtalk', True))
                self.forward_filtered_wechat_var.set(config.get('forward_filtered_wechat', True))
                self.keywords_var.set(', '.join(config.get('keywords', [])))
                self.negative_keywords_var.set(', '.join(config.get('negative_keywords', [])))
                
                logger.info("配置加载成功")
            else:
                logger.warning(f"配置文件 {self.config_file} 不存在，将使用默认配置")
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                'dingtalk_group_name': self.dingtalk_group_var.get(),
                'wechat_group_name': self.wechat_group_var.get(),
                'check_interval': self.check_interval_var.get(),
                'forward_all_dingtalk': self.forward_all_dingtalk_var.get(),
                'forward_filtered_wechat': self.forward_filtered_wechat_var.get(),
                'keywords': [k.strip() for k in self.keywords_var.get().split(',') if k.strip()],
                'negative_keywords': [k.strip() for k in self.negative_keywords_var.get().split(',') if k.strip()],
                'max_reconnect_attempts': 3,
                'reconnect_delay': 10,
                'log_level': "INFO",
                'message_format': {
                    'dingtalk_to_wechat': "【钉钉-{sender}】: {content}",
                    'wechat_to_dingtalk': "【微信-{sender}】: {content}"
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            logger.info("配置保存成功")
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def update_log(self):
        """更新日志显示"""
        while not self.log_queue.empty():
            log_entry = self.log_queue.get()
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, log_entry + '\n')
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        
        self.root.after(100, self.update_log)
    
    def clear_log(self):
        """清空日志显示"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def save_log(self):
        """保存日志到文件"""
        try:
            with open('messenger_gui.log', 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            messagebox.showinfo("成功", "日志已保存到 messenger_gui.log")
        except Exception as e:
            logger.error(f"保存日志失败: {e}")
            messagebox.showerror("错误", f"保存日志失败: {e}")
    
    def update_status_display(self):
        """更新状态显示"""
        try:
            if os.path.exists('status.json'):
                with open('status.json', 'r', encoding='utf-8') as f:
                    status = json.load(f)
                
                self.status_text.config(state=tk.NORMAL)
                self.status_text.delete(1.0, tk.END)
                
                self.status_text.insert(tk.END, "=== 运行状态 ===\n")
                self.status_text.insert(tk.END, f"启动时间: {status.get('start_time', 'N/A')}\n")
                self.status_text.insert(tk.END, f"最后更新: {status.get('last_update', 'N/A')}\n")
                self.status_text.insert(tk.END, f"钉钉连接: {'已连接' if status.get('dingtalk_connected', False) else '未连接'}\n")
                self.status_text.insert(tk.END, f"微信连接: {'已连接' if status.get('wechat_connected', False) else '未连接'}\n")
                self.status_text.insert(tk.END, f"钉钉消息数: {status.get('dingtalk_message_count', 0)}\n")
                self.status_text.insert(tk.END, f"微信消息数: {status.get('wechat_message_count', 0)}\n\n")
                
                self.status_text.insert(tk.END, "=== 最近错误 ===\n")
                errors = status.get('errors', [])
                if errors:
                    for error in errors:
                        self.status_text.insert(tk.END, f"{error.get('time', 'N/A')}: {error.get('message', 'N/A')}\n")
                else:
                    self.status_text.insert(tk.END, "无错误记录\n")
                
                self.status_text.config(state=tk.DISABLED)
            else:
                self.status_text.config(state=tk.NORMAL)
                self.status_text.delete(1.0, tk.END)
                self.status_text.insert(tk.END, "状态文件不存在，请先启动程序")
                self.status_text.config(state=tk.DISABLED)
        except Exception as e:
            logger.error(f"更新状态显示失败: {e}")
            self.status_text.config(state=tk.NORMAL)
            self.status_text.delete(1.0, tk.END)
            self.status_text.insert(tk.END, f"更新状态显示失败: {e}")
            self.status_text.config(state=tk.DISABLED)
    
    def start(self):
        """启动消息转发"""
        if self.running:
            return
        
        # 保存当前配置
        self.save_config()
        
        # 启动工作线程
        self.running = True
        self.worker_thread = threading.Thread(target=self._run_worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        
        # 更新按钮状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        
        logger.info("消息转发已启动")
    
    def _run_worker(self):
        """工作线程函数"""
        try:
            self.start_callback()
        except Exception as e:
            logger.error(f"启动失败: {e}")
            self.running = False
            self.root.after(0, self._update_buttons_after_stop)
    
    def stop(self):
        """停止消息转发"""
        if not self.running:
            return
        
        # 停止工作线程
        self.running = False
        try:
            self.stop_callback()
        except Exception as e:
            logger.error(f"停止失败: {e}")
        
        # 更新按钮状态
        self._update_buttons_after_stop()
        
        logger.info("消息转发已停止")
    
    def _update_buttons_after_stop(self):
        """停止后更新按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def on_closing(self):
        """窗口关闭时的处理"""
        if self.running:
            if messagebox.askokcancel("退出", "程序正在运行，确定要退出吗？"):
                self.stop()
                self.root.destroy()
        else:
            self.root.destroy()
