﻿#Software: 韵达自动化扫描客户端
#ProcessID: 16128 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,584M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2023-12-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:35:50.957 15 Y P KuangScannerNet 准备开始监听4个服务器
18:35:50.961 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:35:51.024 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:35:51.044 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:35:51.046 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:35:51.046 15 Y P KuangScannerNet 准备就绪！
18:35:51.063 15 Y P Http 准备开始监听2个服务器
18:35:51.068 15 Y P Http 开始监听 tcp://0.0.0.0:8080
18:35:51.069 15 Y P Http 开始监听 tcp://[::]:8080
18:35:51.069 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 13600 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,354M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2023-12-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:39:20.347 15 Y P KuangScannerNet 准备开始监听4个服务器
18:39:20.448 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:39:20.456 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:39:20.468 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:39:20.468 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:39:20.468 15 Y P KuangScannerNet 准备就绪！
18:39:20.482 15 Y P Http 准备开始监听2个服务器
18:39:20.502 15 Y P Http 开始监听 tcp://0.0.0.0:8080
18:39:20.502 15 Y P Http 开始监听 tcp://[::]:8080
18:39:20.503 15 Y P Http 准备就绪！
18:40:40.754  1 N - NewLife.Core v10.0.2022.0901 Build 2023-09-01 .NETCoreApp,Version=v6.0
18:40:40.754  1 N - NewLife组件核心库 ©2002-2022 NewLife
18:40:40.754  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
18:40:40.754  1 N - 韵达自动化扫描客户端 Copyright ©  2022
18:40:40.756  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
18:40:40.756  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 8304 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,690M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2023-12-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:40:53.362  5 Y P KuangScannerNet 准备开始监听4个服务器
18:40:53.434  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:40:53.434  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:40:53.452  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:40:53.456  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:40:53.457  5 Y P KuangScannerNet 准备就绪！
18:40:53.468  5 Y P Http 准备开始监听2个服务器
18:40:53.469  5 Y P Http 开始监听 tcp://0.0.0.0:8080
18:40:53.469  5 Y P Http 开始监听 tcp://[::]:8080
18:40:53.469  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 5896 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,711M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2023-12-24
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:44:19.849  5 Y P KuangScannerNet 准备开始监听4个服务器
18:44:19.857  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:44:19.908  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:44:19.911  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:44:19.912  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:44:19.912  5 Y P KuangScannerNet 准备就绪！
18:44:19.926  5 Y P Http 准备开始监听2个服务器
18:44:19.928  5 Y P Http 开始监听 tcp://0.0.0.0:8080
18:44:19.929  5 Y P Http 开始监听 tcp://[::]:8080
18:44:19.929  5 Y P Http 准备就绪！
