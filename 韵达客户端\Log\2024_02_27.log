﻿#Software: 韵达自动化扫描客户端
#ProcessID: 12492 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,103M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-02-27
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:24:01.119  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
06:24:01.122  1 N - NewLife组件核心库 ©2002-2022 NewLife
06:24:01.122  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
06:24:01.122  1 N - 韵达自动化扫描客户端 Copyright ©  2022
06:24:01.122  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
06:24:01.198  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 11664 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,748M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-02-27
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:30:31.739  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
06:30:31.741  1 N - NewLife组件核心库 ©2002-2022 NewLife
06:30:31.742  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
06:30:31.742  1 N - 韵达自动化扫描客户端 Copyright ©  2022
06:30:31.742  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
06:30:31.751  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 8196 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,429M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-27
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:40:51.459  7 Y P KuangScannerNet 准备开始监听4个服务器
06:40:51.463  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:40:51.464  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:40:51.467  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:40:51.467  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:40:51.467  7 Y P KuangScannerNet 准备就绪！
06:40:51.481  7 Y P Http 准备开始监听2个服务器
06:40:51.482  7 Y P Http 开始监听 tcp://0.0.0.0:8080
06:40:51.482  7 Y P Http 开始监听 tcp://[::]:8080
06:40:51.482  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 13504 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,393M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32762/1000
#Date: 2024-02-27
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
09:41:05.996  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
09:41:05.996  1 N - NewLife组件核心库 ©2002-2022 NewLife
09:41:05.997  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
09:41:05.997  1 N - 韵达自动化扫描客户端 Copyright ©  2022
09:41:05.997  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
09:41:05.997  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 17300 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 773M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-02-27
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
09:41:15.359  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
09:41:15.361  1 N - NewLife组件核心库 ©2002-2022 NewLife
09:41:15.361  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
09:41:15.361  1 N - 韵达自动化扫描客户端 Copyright ©  2022
09:41:15.361  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
09:41:15.374  1 N - 异常退出！
