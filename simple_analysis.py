#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单分析脚本 - 不依赖外部库
"""

def analyze_string(encrypted_str):
    """
    分析字符串特征
    """
    print(f"=== 字符串分析 ===")
    print(f"原始字符串: {encrypted_str}")
    print(f"字符串长度: {len(encrypted_str)}")
    
    # ASCII值
    ascii_values = [ord(c) for c in encrypted_str]
    print(f"ASCII值: {ascii_values}")
    
    # 检查字符类型
    digits = [c for c in encrypted_str if c.isdigit()]
    letters = [c for c in encrypted_str if c.isalpha()]
    print(f"数字字符: {digits}")
    print(f"字母字符: {letters}")
    
    # 字符频率
    char_count = {}
    for c in encrypted_str:
        char_count[c] = char_count.get(c, 0) + 1
    print(f"字符频率: {char_count}")
    
    # 检查是否是Base64
    import base64
    try:
        decoded = base64.b64decode(encrypted_str)
        print(f"Base64解码成功: {decoded.hex()}")
    except:
        print("不是有效的Base64编码")
    
    # 检查是否是十六进制
    try:
        if len(encrypted_str) % 2 == 0:
            hex_value = int(encrypted_str, 16)
            print(f"十六进制值: {hex_value}")
    except:
        print("不是有效的十六进制")
    
    # 尝试简单的异或解码
    print("\n=== 尝试简单异或解码 ===")
    for key in range(10):  # 只尝试前10个密钥
        decoded = bytes([ord(c) ^ key for c in encrypted_str])
        try:
            decoded_str = decoded.decode('utf-8')
            if decoded_str.isprintable():
                print(f"异或密钥 {key}: {decoded_str}")
        except:
            pass

if __name__ == "__main__":
    encrypted_str = "cucpfo9yhx1mxgd4"
    analyze_string(encrypted_str) 