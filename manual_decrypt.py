#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动解密分析 - 基于伪代码发现的信息
"""

import base64
import json
import hashlib

def analyze_encrypted_structure(data: bytes):
    """分析加密数据结构"""
    print("=== 加密数据结构分析 ===")
    print(f"总长度: {len(data)} 字节")
    print(f"是否为16的倍数: {len(data) % 16 == 0}")
    
    # 分析前几个块
    for i in range(min(4, len(data) // 16)):
        block = data[i*16:(i+1)*16]
        print(f"块 {i+1}: {block.hex()}")
    
    # 检查是否有重复块（ECB模式特征）
    blocks = [data[i:i+16] for i in range(0, len(data), 16)]
    unique_blocks = set(blocks)
    print(f"总块数: {len(blocks)}, 唯一块数: {len(unique_blocks)}")
    
    if len(blocks) != len(unique_blocks):
        print("⚠️  发现重复块，可能是ECB模式")
    else:
        print("✓ 无重复块，可能是CBC模式或其他")

def try_key_variations(data: bytes, base_key: str):
    """尝试密钥的各种变体"""
    print(f"\n=== 尝试密钥变体: {base_key} ===")
    
    variations = [
        base_key,
        base_key.upper(),
        base_key.lower(),
        hashlib.md5(base_key.encode()).hexdigest()[:16],
        hashlib.sha1(base_key.encode()).hexdigest()[:16],
        hashlib.sha256(base_key.encode()).hexdigest()[:16],
    ]
    
    for i, key in enumerate(variations):
        print(f"\n--- 变体 {i+1}: {key} ---")
        result = try_decrypt_methods(data, key)
        if result:
            return result
    
    return None

def try_decrypt_methods(data: bytes, key: str):
    """尝试多种解密方法"""
    key_bytes = key.encode('utf-8')[:16].ljust(16, b'\x00')
    
    methods = [
        ("XOR循环", xor_decrypt),
        ("XOR固定", lambda d, k: bytes(a ^ b for a, b in zip(d, k * (len(d) // len(k) + 1)))),
        ("简单替换", simple_substitution),
    ]
    
    for method_name, method_func in methods:
        try:
            decrypted = method_func(data, key_bytes)
            
            # 检查解密结果
            if is_valid_decryption(decrypted):
                print(f"✓ {method_name} 可能成功!")
                try:
                    result_str = decrypted.decode('utf-8')
                    print(f"UTF-8解码成功: {result_str[:100]}...")
                    return result_str
                except:
                    print(f"UTF-8解码失败，尝试其他编码...")
                    for encoding in ['latin-1', 'gbk', 'cp1252']:
                        try:
                            result_str = decrypted.decode(encoding)
                            if len([c for c in result_str[:100] if c.isprintable()]) > 50:
                                print(f"{encoding}解码成功: {result_str[:100]}...")
                                return result_str
                        except:
                            continue
        except Exception as e:
            print(f"{method_name} 失败: {e}")
    
    return None

def xor_decrypt(data: bytes, key: bytes) -> bytes:
    """XOR解密"""
    return bytes(data[i] ^ key[i % len(key)] for i in range(len(data)))

def simple_substitution(data: bytes, key: bytes) -> bytes:
    """简单替换解密"""
    # 使用密钥生成替换表
    key_sum = sum(key) % 256
    return bytes((b - key_sum) % 256 for b in data)

def is_valid_decryption(data: bytes) -> bool:
    """检查解密结果是否有效"""
    if len(data) < 10:
        return False
    
    # 检查可打印字符比例
    printable_count = sum(1 for b in data[:100] if 32 <= b <= 126 or b in [9, 10, 13])
    printable_ratio = printable_count / min(100, len(data))
    
    # 检查是否包含JSON特征
    has_json_chars = b'{' in data[:50] or b'"' in data[:50]
    
    # 检查是否包含常见字符串
    common_strings = [b'sessionId', b'token', b'digest', b'password', b'data']
    has_common_strings = any(s in data for s in common_strings)
    
    return printable_ratio > 0.7 or has_json_chars or has_common_strings

def analyze_pseudocode_context():
    """分析伪代码上下文"""
    print("\n=== 伪代码上下文分析 ===")
    print("发现的关键信息:")
    print("1. 密钥: k301qsjbrh1s6ega (16字符)")
    print("2. 算法: AES-ECB")
    print("3. 上下文: 第9756行，用于加密操作")
    print("4. 相关字段: [digest], [password], [reqTime], [token]")
    print("5. 数据格式: 可能是JSON")

def main():
    """主函数"""
    # 乡镇驿站扫描的加密数据
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    print("手动解密分析 - 乡镇驿站扫描数据")
    print("=" * 60)
    
    # Base64解码
    try:
        decoded_data = base64.b64decode(encrypted_data)
        print(f"✓ Base64解码成功，数据长度: {len(decoded_data)}")
    except Exception as e:
        print(f"❌ Base64解码失败: {e}")
        return
    
    # 分析数据结构
    analyze_encrypted_structure(decoded_data)
    
    # 分析伪代码上下文
    analyze_pseudocode_context()
    
    # 使用发现的密钥
    real_key = "k301qsjbrh1s6ega"
    
    print(f"\n🔑 使用发现的密钥: {real_key}")
    
    # 尝试解密
    result = try_key_variations(decoded_data, real_key)
    
    if result:
        print(f"\n🎉 解密成功!")
        print(f"解密结果长度: {len(result)}")
        
        # 尝试解析JSON
        try:
            json_data = json.loads(result)
            print("✓ 解密结果是有效的JSON!")
            print("JSON内容:")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
            
            # 保存结果
            with open('decrypted_json.json', 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            print("✓ JSON结果已保存到 decrypted_json.json")
            
        except json.JSONDecodeError:
            print("解密结果不是JSON格式，保存为文本文件")
            with open('decrypted_text.txt', 'w', encoding='utf-8') as f:
                f.write(result)
            print("✓ 文本结果已保存到 decrypted_text.txt")
            
    else:
        print("\n❌ 解密失败")
        print("可能的原因:")
        print("1. 需要真正的AES库进行解密")
        print("2. 可能需要IV（初始化向量）")
        print("3. 可能使用了不同的填充方式")
        print("4. 密钥可能需要进一步处理")
        
        # 显示原始数据供进一步分析
        print(f"\n原始加密数据(前64字节):")
        print(decoded_data[:64].hex())
        
        # 尝试寻找模式
        print(f"\n数据模式分析:")
        for i in range(0, min(64, len(decoded_data)), 16):
            chunk = decoded_data[i:i+16]
            print(f"偏移 {i:02x}: {chunk.hex()}")

if __name__ == "__main__":
    main()
