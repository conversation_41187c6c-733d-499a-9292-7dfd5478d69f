
def decode_tracking_number(encrypted_number: str) -> dict:
    """
    Decode encrypted tracking number based on analysis
    Format: 530001 + 9XXX (where XXX are 3 additional digits)
    """
    result = {
        'valid': False,
        'prefix': None,
        'suffix': None,
        'full_number': None,
        'errors': []
    }
    
    try:
        # Check if number starts with 530001
        if not encrypted_number.startswith('530001'):
            result['errors'].append('Invalid prefix - must start with 530001')
            return result
        
        # Extract the suffix (should be 4 digits starting with 9)
        suffix = encrypted_number[6:]  # Remove 530001 prefix
        
        if len(suffix) != 4:
            result['errors'].append(f'Invalid suffix length - expected 4 digits, got {len(suffix)}')
            return result
        
        if not suffix[0] == '9':
            result['errors'].append('Invalid first digit - must start with 9')
            return result
        
        if not suffix.isdigit():
            result['errors'].append('Suffix must contain only digits')
            return result
        
        result['valid'] = True
        result['prefix'] = '530001'
        result['suffix'] = suffix
        result['full_number'] = encrypted_number
        
    except Exception as e:
        result['errors'].append(f'Decoding error: {str(e)}')
    
    return result

def generate_valid_tracking_numbers(count: int = 10) -> list:
    """Generate valid tracking numbers for testing"""
    numbers = []
    for i in range(count):
        # Generate 3 random digits after 9
        suffix_digits = str(i).zfill(3)[-3:]  # Ensure 3 digits
        tracking_number = f"5300019{suffix_digits}"
        numbers.append(tracking_number)
    return numbers
