#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四个菜单区分机制分析脚本
"""

import json

def analyze_menu_differentiation():
    """分析四个菜单的区分机制"""

    print("🔍 四个菜单区分机制分析")
    print("=" * 60)

    # 基于伪代码分析的发现
    analysis_results = {
        "共同端点": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1",
        "区分机制": {
            "发现的关键信息": [
                "1. 所有菜单共用同一个API端点",
                "2. 在伪代码第430行和8766行发现长度==4的验证逻辑",
                "3. 在伪代码第17898行发现长度<4的验证逻辑",
                "4. 找到了JSON构建的核心代码段(第9654-9655行)",
                "5. 发现了AES-ECB加密和Base64编码的使用"
            ],
            "最可能的方案": "JSON载荷中的字段区分 + 业务逻辑验证"
        },

        "关键代码发现": {
            "JSON模板": {
                "line": "9654-9655",
                "content": '{"appType":"1","company":"[company]","device":"[device]","digest":"[digest]","password":"[password]","reqTime":"[reqTime]","uniqueCode":"[token]","user":"[user]","version":"3.6.8.0821"}'
            },
            "长度验证": {
                "line_430": "if ( (unsigned int)sub_41F461(1, v12, 0, -2147483644) == 4 )",
                "line_8766": "if ( (unsigned int)sub_41F461(1, v12, 0, -2147483644) == 4 )",
                "line_17898": "if ( *(int *)&v194[16] < 4 )"
            },
            "加密信息": {
                "key": "k301qsjbrh1s6ega",
                "method": "AES-ECB",
                "encoding": "Base64"
            }
        },

        "四个菜单": {
            "1_派件到达": {
                "name": "派件到达",
                "business_rule": "固定设置分拨中心代码530001",
                "validation": "无特殊长度验证",
                "possible_json_fields": {
                    "operationType": "delivery_arrival",
                    "scanType": "1",
                    "action": "delivery",
                    "menuType": "1",
                    "distributionCenter": "530001"
                }
            },
            "2_到派一体": {
                "name": "到派一体",
                "business_rule": "单号输入时长度等于4的代码验证",
                "validation": "length == 4",
                "possible_json_fields": {
                    "operationType": "dispatch_integration",
                    "scanType": "2",
                    "action": "dispatch",
                    "menuType": "2",
                    "codeLength": 4
                }
            },
            "3_乡镇驿站扫描": {
                "name": "乡镇驿站扫描",
                "business_rule": "单号输入时长度等于4的代码验证",
                "validation": "length == 4",
                "possible_json_fields": {
                    "operationType": "rural_station_scan",
                    "scanType": "3",
                    "action": "rural_scan",
                    "menuType": "3",
                    "codeLength": 4
                }
            },
            "4_集包扫描": {
                "name": "集包扫描",
                "business_rule": "单号输入时开头为9的代码 + 下一站为预设6位数代码",
                "validation": "prefix == 9 && nextStationLength == 6",
                "possible_json_fields": {
                    "operationType": "package_collection",
                    "scanType": "4",
                    "action": "collection",
                    "menuType": "4",
                    "trackingPrefix": "9",
                    "nextStationLength": 6
                }
            }
        },
        
        "推测的JSON结构": {
            "common_fields": {
                "appType": "1",
                "company": "[company]",
                "device": "[device]", 
                "digest": "[digest]",
                "password": "[password]",
                "reqTime": "[reqTime]",
                "uniqueCode": "[token]",
                "user": "[user]",
                "version": "3.6.8.0821"
            },
            "differentiation_field": {
                "field_name": "scanType 或 operationType 或 menuType",
                "values": {
                    "1": "派件到达",
                    "2": "到派一体", 
                    "3": "乡镇驿站扫描",
                    "4": "集包扫描"
                }
            },
            "business_data": {
                "trackingNumber": "[扫描的单号]",
                "stationCode": "[站点代码]",
                "distributionCenter": "[分拨中心代码]",
                "nextStation": "[下一站代码]"
            }
        },
        
        "验证方法": {
            "抓包分析": [
                "1. 分别操作四个菜单功能",
                "2. 抓取每个菜单的网络请求",
                "3. 对比请求体的差异",
                "4. 识别区分字段"
            ],
            "代码分析": [
                "1. 搜索伪代码中的菜单相关函数",
                "2. 查找JSON构建逻辑",
                "3. 识别条件分支语句",
                "4. 找到字段赋值逻辑"
            ]
        },
        
        "关键发现": {
            "认证格式": "网络代码 + 员工ID + 密码 + 分拨中心代码 + 设备SN",
            "加密方式": "AES-ECB + Base64",
            "密钥": "k301qsjbrh1s6ega",
            "共同端点": "所有四个菜单使用相同的API端点",
            "区分依据": "很可能通过JSON载荷中的特定字段进行区分"
        }
    }
    
    return analysis_results

def generate_test_requests():
    """生成测试请求示例"""
    
    print("\n📝 生成测试请求示例")
    print("-" * 40)
    
    base_payload = {
        "appType": "1",
        "company": "[company]",
        "device": "[device]", 
        "digest": "[digest]",
        "password": "[password]",
        "reqTime": "[reqTime]",
        "uniqueCode": "[token]",
        "user": "[user]",
        "version": "3.6.8.0821"
    }
    
    test_requests = {
        "1_派件到达": {
            **base_payload,
            "scanType": "1",
            "operationType": "delivery_arrival",
            "distributionCenter": "530001",
            "trackingNumber": "test123456789"
        },
        "2_到派一体": {
            **base_payload,
            "scanType": "2", 
            "operationType": "dispatch_integration",
            "stationCode": "1234",
            "trackingNumber": "test123456789"
        },
        "3_乡镇驿站扫描": {
            **base_payload,
            "scanType": "3",
            "operationType": "rural_station_scan", 
            "stationCode": "5678",
            "trackingNumber": "test123456789"
        },
        "4_集包扫描": {
            **base_payload,
            "scanType": "4",
            "operationType": "package_collection",
            "trackingNumber": "9123456789",
            "nextStation": "530001"
        }
    }
    
    return test_requests

def main():
    """主函数"""
    
    # 分析四个菜单的区分机制
    analysis = analyze_menu_differentiation()
    
    # 生成测试请求
    test_requests = generate_test_requests()
    
    # 保存分析结果
    with open('menu_differentiation_analysis.json', 'w', encoding='utf-8') as f:
        json.dump({
            "analysis": analysis,
            "test_requests": test_requests
        }, f, indent=2, ensure_ascii=False)
    
    # 打印关键信息
    print("\n🎯 关键结论")
    print("-" * 20)
    print("1. 四个菜单共用同一个API端点")
    print("2. 区分机制很可能是JSON载荷中的字段")
    print("3. 可能的区分字段: scanType, operationType, menuType")
    print("4. 每个菜单有不同的业务逻辑验证规则")
    
    print("\n💡 建议的验证方法")
    print("-" * 25)
    print("1. 抓包对比四个菜单的实际请求")
    print("2. 在伪代码中搜索JSON构建逻辑")
    print("3. 查找条件分支和字段赋值")
    print("4. 测试不同的区分字段值")
    
    print(f"\n✅ 分析结果已保存到 menu_differentiation_analysis.json")

if __name__ == "__main__":
    main()
