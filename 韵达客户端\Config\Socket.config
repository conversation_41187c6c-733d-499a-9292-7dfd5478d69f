<?xml version="1.0" encoding="utf-8"?>
<Socket>
  <!--网络调试-->
  <Debug>false</Debug>
  <!--会话超时时间。默认20*60秒-->
  <SessionTimeout>1200</SessionTimeout>
  <!--缓冲区大小。默认8k-->
  <BufferSize>8192</BufferSize>
  <!--收发日志数据体长度。默认64-->
  <LogDataLength>64</LogDataLength>
  <!--启用Http压缩。内部新建的HttpClient将自动添加接受压缩的头部，并在响应中对压缩进行解码，默认true-->
  <EnableHttpCompression>true</EnableHttpCompression>
  <!--自动启用GZip压缩的请求体大小。默认1024，用0表示不压缩-->
  <AutoGZip>1024</AutoGZip>
</Socket>