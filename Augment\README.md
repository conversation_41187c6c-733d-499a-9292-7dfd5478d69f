# 安卓应用自动抓包工具

这个项目提供了两种方式帮助您抓取安卓应用的网络通信数据：

1. **手机端抓包指南**：使用HttpCanary等工具直接在安卓设备上抓包的详细教程
2. **电脑端抓包工具**：一个简单的Python程序，可以在电脑上抓取安卓设备的网络流量

## 文件说明

- `安卓应用抓包指南.md` - 详细介绍如何使用HttpCanary在安卓设备上抓包的教程
- `auto_android_capture.py` - 电脑端自动抓包工具源代码

## 电脑端抓包工具使用方法

### 前提条件

- Python 3.6 或更高版本
- 安卓设备和电脑连接到同一个WiFi网络
- 安装必要的依赖：`pip install scapy tkinter`

### 使用步骤

1. 运行程序：
   ```
   python auto_android_capture.py
   ```

2. 在程序界面中输入您安卓设备的IP地址
   - 可以在安卓设备的设置 > 关于手机 > 状态信息 > IP地址 中查看

3. 点击"开始抓包"按钮

4. 在安卓设备上使用您想要分析的应用

5. 完成后，点击"停止抓包"按钮

6. 点击"保存抓包结果"按钮，将捕获的数据保存为PCAP文件

7. 可以使用Wireshark等工具打开保存的PCAP文件进行详细分析

## 注意事项

- 抓包需要一定的网络和系统权限，请确保您有权限进行这些操作
- 某些应用可能实施了防抓包措施，可能导致抓包失败
- 请尊重隐私和法律法规，不要用于非法目的

## 常见问题

### 无法捕获HTTPS流量
- 电脑端工具可以捕获加密的HTTPS流量，但无法查看其内容
- 如需查看HTTPS内容，建议使用HttpCanary等支持SSL解密的工具

### 找不到设备IP
- 确保设备连接到WiFi
- 在设备的WiFi设置中查看IP地址
- 可以使用网络扫描工具找到设备IP

### 程序无法启动
- 确保已安装所需的Python库：`pip install scapy tkinter`
- 在Windows系统上可能需要以管理员权限运行
