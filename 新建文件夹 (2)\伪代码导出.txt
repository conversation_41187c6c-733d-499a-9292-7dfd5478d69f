import idaapi
import idautils
import idc
import os

# 输出路径设置为桌面
desktop_path = os.path.join("C:\\Users\\<USER>\\Desktop", "all_pseudocode_output.txt")

with open(desktop_path, "w", encoding="utf-8") as f:
    for ea in idautils.Functions():
        try:
            func_name = idc.get_func_name(ea)
            f.write(f"\n// ===== Function: {func_name} =====\n")
            cfunc = idaapi.decompile(ea)
            pseudocode = cfunc.get_pseudocode()
            for line in pseudocode:
                f.write(idaapi.tag_remove(line.line) + "\n")
        except Exception as e:
            f.write(f"// Failed to decompile function at {hex(ea)}: {str(e)}\n")

print(f"[+] 所有伪代码已成功导出到桌面：{desktop_path}")