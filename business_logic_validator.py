
def validate_delivery_arrival(distribution_center_code: str) -> dict:
    """
    派件到达验证 - 固定设置分拨中心代码 530001
    """
    result = {
        'valid': distribution_center_code == '530001',
        'code': distribution_center_code,
        'type': 'delivery_arrival',
        'description': '派件到达'
    }
    if not result['valid']:
        result['error'] = f'分拨中心代码错误，应为530001，实际为{distribution_center_code}'
    return result

def validate_dispatch_integration(input_code: str) -> dict:
    """
    到派一体验证 - 单号输入时长度等于4的代码
    """
    result = {
        'valid': len(input_code) == 4 and input_code.isdigit(),
        'code': input_code,
        'type': 'dispatch_integration',
        'description': '到派一体'
    }
    if not result['valid']:
        if len(input_code) != 4:
            result['error'] = f'代码长度错误，应为4位，实际为{len(input_code)}位'
        elif not input_code.isdigit():
            result['error'] = '代码必须为纯数字'
    return result

def validate_rural_station_scan(input_code: str) -> dict:
    """
    乡镇驿站扫描验证 - 单号输入时长度等于4的代码
    """
    result = {
        'valid': len(input_code) == 4 and input_code.isdigit(),
        'code': input_code,
        'type': 'rural_station_scan',
        'description': '乡镇驿站扫描'
    }
    if not result['valid']:
        if len(input_code) != 4:
            result['error'] = f'代码长度错误，应为4位，实际为{len(input_code)}位'
        elif not input_code.isdigit():
            result['error'] = '代码必须为纯数字'
    return result

def validate_package_collection(tracking_number: str, next_station_code: str) -> dict:
    """
    集包扫描验证 - 单号开头为9 + 下一站为预设6位数代码
    """
    result = {
        'valid': False,
        'tracking_number': tracking_number,
        'next_station_code': next_station_code,
        'type': 'package_collection',
        'description': '集包扫描',
        'errors': []
    }

    # 检查单号是否以9开头
    if not tracking_number.startswith('9'):
        result['errors'].append('单号必须以9开头')

    # 检查下一站代码是否为6位数字
    if len(next_station_code) != 6:
        result['errors'].append(f'下一站代码长度错误，应为6位，实际为{len(next_station_code)}位')
    elif not next_station_code.isdigit():
        result['errors'].append('下一站代码必须为纯数字')

    result['valid'] = len(result['errors']) == 0
    return result

def test_tracking_numbers():
    """测试真实的快递单号"""
    test_numbers = ['312799099115886', '434640497727147']

    print("=== 真实快递单号测试 ===")
    for number in test_numbers:
        print(f"\n单号: {number}")
        print(f"长度: {len(number)}")
        print(f"首位: {number[0]}")
        print(f"是否以9开头: {number.startswith('9')}")

        # 测试集包扫描验证
        result = validate_package_collection(number, '530001')
        print(f"集包扫描验证: {'通过' if result['valid'] else '失败'}")
        if result['errors']:
            print(f"错误: {', '.join(result['errors'])}")

def test_business_logic():
    """测试所有业务逻辑"""
    print("=== 业务逻辑验证测试 ===")

    # 测试派件到达
    print("\n1. 派件到达测试:")
    test_cases = ['530001', '530002', '123456']
    for code in test_cases:
        result = validate_delivery_arrival(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试到派一体
    print("\n2. 到派一体测试:")
    test_cases = ['1234', '12345', 'abcd', '123']
    for code in test_cases:
        result = validate_dispatch_integration(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试乡镇驿站扫描
    print("\n3. 乡镇驿站扫描测试:")
    test_cases = ['5678', '567', '56789', 'abcd']
    for code in test_cases:
        result = validate_rural_station_scan(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试集包扫描
    print("\n4. 集包扫描测试:")
    test_cases = [
        ('912345678901234', '530001'),
        ('812345678901234', '530001'),
        ('912345678901234', '53001'),
        ('912345678901234', 'abcdef')
    ]
    for tracking, station in test_cases:
        result = validate_package_collection(tracking, station)
        print(f"  {tracking[:10]}... + {station}: {'通过' if result['valid'] else '失败'}")
        if result['errors']:
            print(f"    错误: {', '.join(result['errors'])}")

if __name__ == "__main__":
    test_tracking_numbers()
    test_business_logic()
