#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的快递上传测试脚本
用于验证从IDA分析中提取的关键信息
"""

import json
import hashlib
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

def test_aes_encryption():
    """测试AES加密解密功能"""
    print("=" * 50)
    print("测试AES加密解密")
    print("=" * 50)
    
    # 从IDA分析中提取的密钥
    aes_key = "k301qsjbrh1s6ega"
    
    # 测试数据
    test_data = "这是一个测试数据"
    
    print(f"原始数据: {test_data}")
    print(f"AES密钥: {aes_key}")
    
    try:
        # 加密
        key = aes_key.encode('utf-8')
        cipher = AES.new(key, AES.MODE_ECB)
        padded_data = pad(test_data.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')
        
        print(f"加密结果: {encrypted_b64}")
        
        # 解密
        cipher = AES.new(key, AES.MODE_ECB)
        encrypted_data = base64.b64decode(encrypted_b64)
        decrypted = cipher.decrypt(encrypted_data)
        plaintext = unpad(decrypted, AES.block_size)
        
        print(f"解密结果: {plaintext.decode('utf-8')}")
        
        if test_data == plaintext.decode('utf-8'):
            print("✓ AES加密解密测试成功")
        else:
            print("✗ AES加密解密测试失败")
            
    except Exception as e:
        print(f"✗ AES测试异常: {e}")

def test_login_data_format():
    """测试登录数据格式"""
    print("\n" + "=" * 50)
    print("测试登录数据格式")
    print("=" * 50)
    
    # 从IDA分析中提取的认证信息
    network_code = "530023"      # 网点编号
    employee_id = "9528"         # 业务员工号
    password = "881000"          # 业务员口令
    center_code = "530001"       # 分拨中心代码
    device_sn = "59992231000470" # 设备SN
    version = "3.6.8.0821"      # 版本号
    
    print("认证信息:")
    print(f"  网点编号: {network_code}")
    print(f"  业务员工号: {employee_id}")
    print(f"  业务员口令: {password}")
    print(f"  分拨中心代码: {center_code}")
    print(f"  设备SN: {device_sn}")
    print(f"  版本号: {version}")
    
    # 设备信息JSON
    device_info = {
        "imei": device_sn,
        "mac": "00:11:22:33:44:55",
        "serialNumber": device_sn
    }
    device_json = json.dumps(device_info, separators=(',', ':'))
    
    # 登录数据JSON
    login_data = {
        "appType": "1",
        "company": network_code,
        "device": device_json,
        "digest": "",  # 稍后计算
        "password": password,
        "reqTime": "1640995200000",  # 示例时间戳
        "uniqueCode": "test-unique-code",
        "user": employee_id,
        "version": version
    }
    
    # 计算摘要
    data_str = json.dumps(login_data, separators=(',', ':'))
    md5_hash = hashlib.md5()
    md5_hash.update(data_str.encode('utf-8'))
    digest = md5_hash.hexdigest()
    login_data["digest"] = digest
    
    print(f"\n设备信息JSON:")
    print(device_json)
    
    print(f"\n登录数据JSON:")
    print(json.dumps(login_data, indent=2, ensure_ascii=False))
    
    print(f"\n数据摘要: {digest}")

def test_upload_data_format():
    """测试四种操作类型的上传数据格式"""
    print("\n" + "=" * 50)
    print("测试四种操作类型的上传数据格式")
    print("=" * 50)

    # 四种操作类型配置
    operation_types = {
        "arrival": {      # 到达
            "app_type": "XCRK",
            "operation_code": 24,
            "packet_type": "2(0)"
        },
        "package": {      # 建包
            "app_type": "wddpyt",
            "operation_code": 13,
            "packet_type": "2(1)"
        },
        "town": {         # 乡镇
            "app_type": "wddpyt",
            "operation_code": 13,
            "packet_type": "2(1)"
        },
        "delivery": {     # 到派
            "app_type": "wddpyt",
            "operation_code": 13,
            "packet_type": "2(1)"
        }
    }

    tracking_number = "YT1234567890123"
    timestamp = "1640995200000"

    for op_name, op_config in operation_types.items():
        print(f"\n--- {op_name.upper()} 操作类型 ---")

        # 构建数据包
        packet_type = op_config["packet_type"]
        app_type = op_config["app_type"]
        operation_code = op_config["operation_code"]

        # 基础数据包结构
        packet = {
            f"{packet_type}.4": tracking_number,
            f"{packet_type}.32": "9528",
            f"{packet_type}.41": tracking_number,
            f"{packet_type}.44": tracking_number,
            f"{packet_type}.46": operation_code,
            f"{packet_type}.47": timestamp,
            f"{packet_type}.54": app_type
        }

        json_data = json.dumps(packet, separators=(',', ':'))
        print(f"数据包类型: {packet_type}")
        print(f"应用类型: {app_type}")
        print(f"操作码: {operation_code}")
        print(f"数据包: {json_data[:80]}...")

        # 测试加密
        aes_key = "k301qsjbrh1s6ega"
        try:
            key = aes_key.encode('utf-8')
            cipher = AES.new(key, AES.MODE_ECB)
            padded_data = pad(json_data.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_data)
            encrypted_b64 = base64.b64encode(encrypted).decode('utf-8')

            print(f"加密结果: {encrypted_b64[:50]}...")
            print("✓ 测试成功")

        except Exception as e:
            print(f"✗ 加密失败: {e}")

    print("\n✓ 四种操作类型数据格式测试完成")

def test_network_info():
    """测试网络信息"""
    print("\n" + "=" * 50)
    print("测试网络信息")
    print("=" * 50)
    
    # 从IDA分析中提取的网络信息
    base_url = "http://scan.yundasys.com:9900/rock"
    login_url = f"{base_url}/query/wdBqLogin/v1"
    upload_url = f"{base_url}/upload/outlet/scan/bq/v1"
    
    print("网络配置:")
    print(f"  基础URL: {base_url}")
    print(f"  登录接口: {login_url}")
    print(f"  上传接口: {upload_url}")
    
    # 请求头信息
    headers = {
        "User-Agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
        "Content-Type": "application/octet-stream"
    }
    
    print(f"\n请求头:")
    for key, value in headers.items():
        print(f"  {key}: {value}")

def main():
    """主测试函数"""
    print("快递单号上传脚本 - 关键信息验证")
    print("基于IDA伪代码分析结果")
    
    # 执行各项测试
    test_aes_encryption()
    test_login_data_format()
    test_upload_data_format()
    test_network_info()
    
    print("\n" + "=" * 50)
    print("所有测试完成")
    print("=" * 50)
    
    print("\n关键发现总结:")
    print("1. AES密钥: k301qsjbrh1s6ega")
    print("2. 加密模式: AES-ECB")
    print("3. 服务器: scan.yundasys.com:9900")
    print("4. 版本号: 3.6.8.0821")
    print("\n四种操作类型:")
    print("- 到达 (arrival): XCRK, 操作码24, 数据包2(0)")
    print("- 建包 (package): wddpyt, 操作码13, 数据包2(1)")
    print("- 乡镇 (town): wddpyt, 操作码13, 数据包2(1)")
    print("- 到派 (delivery): wddpyt, 操作码13, 数据包2(1)")
    print("\n认证信息:")
    print("- 网点编号: 530023")
    print("- 工号: 9528")
    print("- 口令: 881000")
    print("- 分拨中心: 530001")
    print("- 设备SN: 59992231000470")

if __name__ == "__main__":
    main()
