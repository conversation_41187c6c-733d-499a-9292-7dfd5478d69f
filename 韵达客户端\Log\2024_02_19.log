﻿#Software: 韵达自动化扫描客户端
#ProcessID: 13196 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,557M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-19
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
07:16:41.079  7 Y P KuangScannerNet 准备开始监听4个服务器
07:16:41.109  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
07:16:41.169  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
07:16:41.192  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
07:16:41.199  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
07:16:41.199  7 Y P KuangScannerNet 准备就绪！
07:16:41.217  7 Y P Http 准备开始监听2个服务器
07:16:41.218  7 Y P Http 开始监听 tcp://0.0.0.0:8080
07:16:41.231  7 Y P Http 开始监听 tcp://[::]:8080
07:16:41.231  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 15372 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,870M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32763/1000
#Date: 2024-02-19
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
12:59:40.688  5 Y P KuangScannerNet 准备开始监听4个服务器
12:59:40.758  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
12:59:40.771  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
12:59:40.774  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
12:59:40.779  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
12:59:40.779  5 Y P KuangScannerNet 准备就绪！
12:59:40.798  5 Y P Http 准备开始监听2个服务器
12:59:40.818  5 Y P Http 开始监听 tcp://0.0.0.0:8080
12:59:40.834  5 Y P Http 开始监听 tcp://[::]:8080
12:59:40.834  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 16548 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,803M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-19
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:01:39.173  5 Y P KuangScannerNet 准备开始监听4个服务器
13:01:39.177  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
13:01:39.179  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
13:01:39.183  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
13:01:39.217  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
13:01:39.217  5 Y P KuangScannerNet 准备就绪！
13:01:39.231  5 Y P Http 准备开始监听2个服务器
13:01:39.283  5 Y P Http 开始监听 tcp://0.0.0.0:8080
13:01:39.290  5 Y P Http 开始监听 tcp://[::]:8080
13:01:39.290  5 Y P Http 准备就绪！
