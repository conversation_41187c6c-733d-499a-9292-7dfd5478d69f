﻿#Software: 韵达自动化扫描客户端
#ProcessID: 15884 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,888M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-01-23
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:08:40.080  7 Y P KuangScannerNet 准备开始监听4个服务器
18:08:40.162  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:08:40.173  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:08:40.176  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:08:40.189  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:08:40.189  7 Y P KuangScannerNet 准备就绪！
18:08:40.206  7 Y P Http 准备开始监听2个服务器
18:08:40.207  7 Y P Http 开始监听 tcp://0.0.0.0:8080
18:08:40.219  7 Y P Http 开始监听 tcp://[::]:8080
18:08:40.219  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 2944 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,883M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-01-23
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:12:08.016  7 Y P KuangScannerNet 准备开始监听4个服务器
18:12:08.078  7 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
18:12:08.078  7 Y P KuangScannerNet 开始监听 tcp://[::]:12345
18:12:08.081  7 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
18:12:08.081  7 Y P KuangScannerNet 开始监听 udp://[::]:12345
18:12:08.081  7 Y P KuangScannerNet 准备就绪！
18:12:08.094  7 Y P Http 准备开始监听2个服务器
18:12:08.094  7 Y P Http 开始监听 tcp://0.0.0.0:8080
18:12:08.095  7 Y P Http 开始监听 tcp://[::]:8080
18:12:08.095  7 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 12876 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,123M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32765/1000
#Date: 2024-01-23
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
18:16:35.765  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
18:16:35.767  1 N - NewLife组件核心库 ©2002-2022 NewLife
18:16:35.767  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
18:16:35.767  1 N - 韵达自动化扫描客户端 Copyright ©  2022
18:16:35.767  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
18:16:35.778  1 N - 异常退出！
