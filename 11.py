import requests
import time
import hashlib
import base64
import json

def generate_digest(data):
    """
    生成正确的digest签名

    算法: 将company+user+password+device+reqTime+data字段按顺序拼接，
    计算MD5哈希值，然后进行base64编码
    """
    # 按照顺序拼接字段
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
        f"{data['data']}"
    )

    # 计算MD5
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()

    # 转换为base64
    digest = base64.b64encode(md5_hash.encode()).decode()

    return digest

def submit_number(number):
    url = "http://scan.yundasys.com:9900/rock/query/waybillMarkingSearch/v1"
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    current_time = time.strftime("%Y-%m-%d %H:%M:%S")

    data = {
        "brandCode": None,
        "company": "886209",
        "data": number,
        "device": "51112315001747",
        "password": "4297f44b13955235245b2497399d7a93",
        "reqTime": current_time,
        "status": None,
        "updateTime": None,
        "user": "1111",
        "version": "3.6.8.0821"
    }

    # 生成digest
    data["digest"] = generate_digest(data)

    print(f"\n请求数据:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print(f"生成的digest: {data['digest']}")

    try:
        print(f"\n发送请求到: {url}")
        response = requests.post(url, json=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        result = response.json()

        if result.get("code") == 0 and result.get("msg") == "成功":
            print(f"\n单号 {number} 查询成功")
            print(f"标记状态: {result['data']}")
            return True
        else:
            print(f"\n单号 {number} 查询失败")
            print(f"错误信息: {json.dumps(result, ensure_ascii=False)}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"\n网络错误: {e}")
        return False
    except json.JSONDecodeError:
        print(f"\n响应格式错误: {response.text}")
        return False

def main():
    numbers = [
        "434544250985806"  # 新添加的单号
    ]

    total = len(numbers)
    print(f"开始处理 {total} 个单号")

    success_count = 0
    for i, number in enumerate(numbers, 1):
        print(f"\n[{i}/{total}] 处理单号: {number}")
        if submit_number(number):
            success_count += 1
        time.sleep(1)  # 请求间隔

    print(f"\n处理完成! 成功: {success_count}/{total}")

if __name__ == "__main__":
    main()