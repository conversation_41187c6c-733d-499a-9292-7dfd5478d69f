import requests
import time
import json
import hashlib
import base64
import itertools
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='digest_cracker.log',
    filemode='w'
)

# 定义可能的字段组合
POSSIBLE_FIELDS = [
    'brandCode', 'company', 'data', 'device', 'password',
    'reqTime', 'status', 'updateTime', 'user', 'version'
]

def generate_digest_v1(data):
    """原始算法: company+user+password+device+reqTime的MD5，然后base64编码"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v2(data):
    """尝试不同顺序: user+company+password+device+reqTime的MD5，然后base64编码"""
    data_str = (
        f"{data['user']}"
        f"{data['company']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v3(data):
    """尝试添加version字段: company+user+password+device+reqTime+version的MD5，然后base64编码"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
        f"{data['version']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v4(data):
    """尝试添加data字段: company+user+password+device+reqTime+data的MD5，然后base64编码"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
        f"{data['data']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v5(data):
    """尝试使用SHA1而不是MD5"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    sha1_hash = hashlib.sha1(data_str.encode()).hexdigest()
    digest = base64.b64encode(sha1_hash.encode()).decode()
    return digest

def generate_digest_v6(data):
    """尝试使用SHA256而不是MD5"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    sha256_hash = hashlib.sha256(data_str.encode()).hexdigest()
    digest = base64.b64encode(sha256_hash.encode()).decode()
    return digest

def generate_digest_v7(data):
    """尝试直接对拼接字符串进行base64编码，不使用哈希"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    digest = base64.b64encode(data_str.encode()).decode()
    return digest

def generate_digest_v8(data):
    """尝试对所有非空字段按字母顺序排序后拼接"""
    fields = []
    for key in sorted(data.keys()):
        if data[key] is not None:
            fields.append(str(data[key]))

    data_str = "".join(fields)
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v9(data):
    """尝试对所有非空字段按字母顺序排序后拼接，使用key=value格式"""
    fields = []
    for key in sorted(data.keys()):
        if data[key] is not None:
            fields.append(f"{key}={data[key]}")

    data_str = "&".join(fields)
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v10(data):
    """尝试对所有字段按字母顺序排序后拼接，包括空值"""
    fields = []
    for key in sorted(data.keys()):
        value = "" if data[key] is None else str(data[key])
        fields.append(value)

    data_str = "".join(fields)
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v11(data):
    """尝试使用MD5(company+user+password+device+reqTime)，不进行base64编码"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    digest = hashlib.md5(data_str.encode()).hexdigest()
    return digest

def generate_digest_v12(data):
    """尝试使用MD5(company+user+password+device+reqTime+salt)，salt为固定值"""
    salt = "yundasys"  # 尝试一个可能的盐值
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
        f"{salt}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v13(data):
    """尝试使用双重MD5"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    md5_hash1 = hashlib.md5(data_str.encode()).hexdigest()
    md5_hash2 = hashlib.md5(md5_hash1.encode()).hexdigest()
    digest = base64.b64encode(md5_hash2.encode()).decode()
    return digest

def generate_digest_v14(data):
    """尝试使用MD5(JSON字符串)"""
    # 移除digest字段，避免循环引用
    data_copy = data.copy()
    if "digest" in data_copy:
        del data_copy["digest"]

    json_str = json.dumps(data_copy, sort_keys=True, ensure_ascii=False)
    md5_hash = hashlib.md5(json_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v15(data):
    """尝试使用MD5(company+data+device+password+reqTime+user+version)，按字段名排序"""
    data_str = (
        f"{data['company']}"
        f"{data['data']}"
        f"{data['device']}"
        f"{data['password']}"
        f"{data['reqTime']}"
        f"{data['user']}"
        f"{data['version']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v16(data):
    """尝试使用HMAC-MD5，以company作为key"""
    import hmac
    key = data['company'].encode()
    message = (
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    ).encode()

    hmac_digest = hmac.new(key, message, hashlib.md5).hexdigest()
    digest = base64.b64encode(hmac_digest.encode()).decode()
    return digest

def generate_digest_v17(data):
    """尝试使用HMAC-MD5，以password作为key"""
    import hmac
    key = data['password'].encode()
    message = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    ).encode()

    hmac_digest = hmac.new(key, message, hashlib.md5).hexdigest()
    digest = base64.b64encode(hmac_digest.encode()).decode()
    return digest

def generate_digest_v18(data):
    """尝试使用MD5(company+user+password+device+reqTime)，然后截取前16位后base64编码"""
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()[:16]  # 只取前16位
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v19(data):
    """尝试使用MD5(reqTime+company+user+password+device)，调整顺序把时间放在最前面"""
    data_str = (
        f"{data['reqTime']}"
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def generate_digest_v20(data):
    """尝试使用MD5(company+user+password+device+reqTime)，但reqTime格式化为yyyyMMddHHmmss"""
    # 将时间格式化为yyyyMMddHHmmss
    try:
        time_obj = time.strptime(data['reqTime'], "%Y-%m-%d %H:%M:%S")
        formatted_time = time.strftime("%Y%m%d%H%M%S", time_obj)
    except:
        formatted_time = data['reqTime'].replace("-", "").replace(":", "").replace(" ", "")

    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{formatted_time}"
    )
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    digest = base64.b64encode(md5_hash.encode()).decode()
    return digest

def collect_hashes():
    """测试多种digest生成算法"""
    url = "http://scan.yundasys.com:9900/rock/query/waybillMarkingSearch/v1"
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # 定义所有digest生成函数
    digest_generators = [
        ("v1_原始算法", generate_digest_v1),
        ("v2_调整顺序", generate_digest_v2),
        ("v3_添加version", generate_digest_v3),
        ("v4_添加data", generate_digest_v4),
        ("v5_使用SHA1", generate_digest_v5),
        ("v6_使用SHA256", generate_digest_v6),
        ("v7_直接Base64", generate_digest_v7),
        ("v8_按字母排序", generate_digest_v8),
        ("v9_key=value格式", generate_digest_v9),
        ("v10_包含空值", generate_digest_v10),
        ("v11_纯MD5不Base64", generate_digest_v11),
        ("v12_添加盐值", generate_digest_v12),
        ("v13_双重MD5", generate_digest_v13),
        ("v14_JSON字符串MD5", generate_digest_v14),
        ("v15_按字段名排序", generate_digest_v15),
        ("v16_HMAC_MD5_Company", generate_digest_v16),
        ("v17_HMAC_MD5_Password", generate_digest_v17),
        ("v18_MD5_前16位", generate_digest_v18),
        ("v19_时间放前面", generate_digest_v19),
        ("v20_时间格式化", generate_digest_v20)
    ]

    successful_algorithms = []

    # 测试每种算法
    for algo_name, generator in digest_generators:
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")

        data = {
            "brandCode": None,
            "company": "886209",
            "data": "434542754941704",
            "device": "51112315001747",
            "password": "4297f44b13955235245b2497399d7a93",
            "reqTime": current_time,
            "status": None,
            "updateTime": None,
            "user": "1111",
            "version": "3.6.8.0821"
        }

        # 生成digest
        digest = generator(data)
        data["digest"] = digest

        print(f"\n测试算法: {algo_name}")
        print("请求数据:", json.dumps(data, ensure_ascii=False, indent=2))
        logging.info(f"测试算法: {algo_name}")
        logging.info(f"生成的digest: {digest}")
        logging.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")

        try:
            response = requests.post(url, json=data, headers=headers)
            print(f"状态码: {response.status_code}")
            print("响应内容:", response.text)

            logging.info(f"状态码: {response.status_code}")
            logging.info(f"响应内容: {response.text}")

            result = response.json()

            if result.get("code") == 0:  # 成功响应
                print(f"请求成功! 找到正确的算法: {algo_name}")
                logging.info(f"请求成功! 找到正确的算法: {algo_name}")
                successful_algorithms.append({
                    "algorithm": algo_name,
                    "digest": digest,
                    "data": data
                })
            else:
                print(f"请求失败: {result.get('msg')}")
                logging.info(f"请求失败: {result.get('msg')}")

            time.sleep(1)  # 间隔1秒

        except Exception as e:
            print(f"错误: {e}")
            logging.error(f"错误: {e}")

    # 保存成功的算法
    if successful_algorithms:
        with open("successful_algorithms.json", "w", encoding='utf-8') as f:
            json.dump(successful_algorithms, indent=2, ensure_ascii=False, fp=f)

        print(f"\n成功找到 {len(successful_algorithms)} 个有效算法")
        print("已保存到 successful_algorithms.json")
    else:
        print("\n没有找到有效的digest生成算法")

        # 尝试更多字段组合
        print("\n尝试更多字段组合...")
        test_field_combinations()

def test_field_combinations():
    """测试不同字段的组合"""
    url = "http://scan.yundasys.com:9900/rock/query/waybillMarkingSearch/v1"
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    # 只测试非空字段
    non_null_fields = ['company', 'data', 'device', 'password', 'reqTime', 'user', 'version']

    # 测试3-5个字段的组合
    for length in range(3, 6):
        for fields in itertools.combinations(non_null_fields, length):
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")

            data = {
                "brandCode": None,
                "company": "886209",
                "data": "434542754941704",
                "device": "51112315001747",
                "password": "4297f44b13955235245b2497399d7a93",
                "reqTime": current_time,
                "status": None,
                "updateTime": None,
                "user": "1111",
                "version": "3.6.8.0821"
            }

            # 生成digest
            data_str = ""
            for field in fields:
                data_str += str(data[field])

            md5_hash = hashlib.md5(data_str.encode()).hexdigest()
            digest = base64.b64encode(md5_hash.encode()).decode()
            data["digest"] = digest

            field_str = "+".join(fields)
            print(f"\n测试字段组合: {field_str}")
            logging.info(f"测试字段组合: {field_str}")
            logging.info(f"生成的digest: {digest}")

            try:
                response = requests.post(url, json=data, headers=headers)
                result = response.json()

                if result.get("code") == 0:  # 成功响应
                    print(f"请求成功! 找到正确的字段组合: {field_str}")
                    logging.info(f"请求成功! 找到正确的字段组合: {field_str}")

                    # 保存成功的组合
                    with open("successful_combination.json", "w", encoding='utf-8') as f:
                        json.dump({
                            "fields": field_str,
                            "digest": digest,
                            "data": data
                        }, indent=2, ensure_ascii=False, fp=f)

                    return True
                else:
                    print(f"请求失败: {result.get('msg')}")
                    logging.info(f"请求失败: {result.get('msg')}")

                time.sleep(1)  # 间隔1秒

            except Exception as e:
                print(f"错误: {e}")
                logging.error(f"错误: {e}")

    return False

if __name__ == "__main__":
    print("=== 开始破解digest生成算法 ===")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 先测试预定义的算法
    collect_hashes()

    # 如果没有找到有效算法，可以尝试更多组合
    # 注意：这部分可能会发送大量请求，请谨慎使用
    # test_field_combinations()