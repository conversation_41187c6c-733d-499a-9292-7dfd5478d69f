#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集包扫描提交脚本
"""

import json
import time
import base64
import hashlib
import urllib.request
import urllib.parse
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

class PackageCollectionScanner:
    def __init__(self):
        self.api_url = "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1"
        self.login_url = "http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1"
        self.aes_key = "k301qsjbrh1s6ega"
        self.version = "3.6.8.0821"
        
        # 用户提供的信息
        self.network_code = "530023"  # 网点编号
        self.employee_id = "9528"     # 业务员4位工号
        self.password = "881000"      # 业务员口令
        self.distribution_center = "530001"  # 上级分拨中心代码
        self.device_sn = "59992231000470"    # 巴枪SN码
        self.next_station = "530001"  # 下一站
        self.collection_code = "991006108234926"  # 包号(9开头15位数字)
    
    def encrypt_password(self, password):
        """使用AES-ECB + Base64加密密码"""
        try:
            cipher = AES.new(self.aes_key.encode('utf-8'), AES.MODE_ECB)
            padded_password = pad(password.encode('utf-8'), AES.block_size)
            encrypted = cipher.encrypt(padded_password)
            return base64.b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"❌ 密码加密失败: {e}")
            return None
    
    def generate_timestamp(self):
        """生成时间戳"""
        return str(int(time.time() * 1000))
    
    def generate_token(self):
        """生成token (简单示例，实际可能更复杂)"""
        timestamp = self.generate_timestamp()
        token_data = f"{self.network_code}{self.employee_id}{timestamp}"
        return hashlib.md5(token_data.encode()).hexdigest()[:16]
    
    def generate_digest(self):
        """生成认证摘要"""
        # 认证格式：网络代码 + 员工ID + 密码 + 分拨中心代码 + 设备SN
        auth_string = f"{self.network_code}{self.employee_id}{self.password}{self.distribution_center}{self.device_sn}"
        return hashlib.md5(auth_string.encode()).hexdigest()
    
    def build_collection_request(self, tracking_numbers):
        """构建集包扫描请求"""
        encrypted_password = self.encrypt_password(self.password)
        if not encrypted_password:
            return None
        
        # 基础请求数据
        request_data = {
            "appType": "1",
            "company": self.network_code,
            "device": self.device_sn,
            "user": self.employee_id,
            "password": encrypted_password,
            "digest": self.generate_digest(),
            "reqTime": self.generate_timestamp(),
            "uniqueCode": self.generate_token(),
            "version": self.version,
            
            # 集包扫描特有字段
            "operationType": "package_collection",
            "collectionCode": self.collection_code,  # 9开头15位数字包号
            "nextStation": self.next_station,        # 下一站代码
            "distributionCenter": self.distribution_center,
            
            # 快递单号列表
            "trackingNumbers": tracking_numbers if isinstance(tracking_numbers, list) else [tracking_numbers]
        }
        
        return request_data
    
    def submit_collection(self, tracking_numbers):
        """提交集包扫描"""
        print("🚀 开始集包扫描提交...")
        print("-" * 60)
        
        # 验证包号格式
        if not self.collection_code.startswith('9') or len(self.collection_code) != 15:
            print(f"❌ 包号格式错误: {self.collection_code}")
            print("   包号必须是9开头的15位数字")
            return False
        
        # 构建请求
        request_data = self.build_collection_request(tracking_numbers)
        if not request_data:
            print("❌ 请求数据构建失败")
            return False
        
        # 打印请求信息
        print("📋 请求信息:")
        print(f"   网点编号: {self.network_code}")
        print(f"   业务员工号: {self.employee_id}")
        print(f"   设备SN: {self.device_sn}")
        print(f"   包号: {self.collection_code}")
        print(f"   下一站: {self.next_station}")
        print(f"   快递单号: {tracking_numbers}")
        print(f"   加密后密码: {request_data['password']}")
        print(f"   认证摘要: {request_data['digest'][:16]}...")
        print()
        
        # 发送请求
        try:
            print("🌐 发送网络请求...")

            # 准备请求数据
            json_data = json.dumps(request_data).encode('utf-8')

            # 创建请求
            req = urllib.request.Request(
                self.api_url,
                data=json_data,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'YundaScanner/3.6.8.0821',
                    'token': request_data['uniqueCode']
                }
            )

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                status_code = response.getcode()
                response_data = response.read().decode('utf-8')

                print(f"📡 响应状态码: {status_code}")

                if status_code == 200:
                    try:
                        result = json.loads(response_data)
                        print("✅ 请求成功!")
                        print("📄 响应内容:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))
                        return True
                    except json.JSONDecodeError:
                        print("⚠️  响应不是有效的JSON格式")
                        print(f"响应内容: {response_data}")
                        return False
                else:
                    print(f"❌ 请求失败: HTTP {status_code}")
                    print(f"响应内容: {response_data}")
                    return False

        except Exception as e:
            print(f"❌ 网络请求异常: {e}")
            return False
    
    def print_config(self):
        """打印配置信息"""
        print("⚙️  集包扫描配置信息")
        print("=" * 60)
        print(f"API端点: {self.api_url}")
        print(f"网点编号: {self.network_code}")
        print(f"业务员工号: {self.employee_id}")
        print(f"原始密码: {self.password}")
        print(f"加密后密码: {self.encrypt_password(self.password)}")
        print(f"分拨中心: {self.distribution_center}")
        print(f"设备SN: {self.device_sn}")
        print(f"包号: {self.collection_code}")
        print(f"下一站: {self.next_station}")
        print(f"加密密钥: {self.aes_key}")
        print(f"应用版本: {self.version}")
        print("=" * 60)

def main():
    """主函数"""
    scanner = PackageCollectionScanner()
    
    # 打印配置
    scanner.print_config()
    
    # 用户指定的测试快递单号
    target_tracking_number = "312799097135367"

    print(f"\n🎯 测试提交快递单号: {target_tracking_number}")

    # 测试指定的快递单号
    print("\n📦 开始集包扫描提交:")
    success = scanner.submit_collection(target_tracking_number)

    if success:
        print("✅ 提交成功!")
    else:
        print("❌ 提交失败!")
    
    print("\n🏁 测试完成!")

if __name__ == "__main__":
    main()
