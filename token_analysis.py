#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token构成分析
"""

def analyze_token():
    """分析token的构成规律"""
    
    print("🔍 Token构成分析")
    print("=" * 60)
    
    # 已知信息
    token = "530023952859992231000473"
    network_code = "530023"
    employee_id = "9528"
    device_sn = "59992231000470"
    
    print(f"Token: {token}")
    print(f"Token长度: {len(token)} 位")
    print()
    
    print("🧩 已知参数:")
    print(f"网点编号: {network_code} (6位)")
    print(f"业务员工号: {employee_id} (4位)")
    print(f"设备SN: {device_sn} (14位)")
    print()
    
    print("🔍 Token分解分析:")
    
    # 尝试分解token
    if token.startswith(network_code):
        print(f"✅ 开头匹配网点编号: {network_code}")
        remaining = token[len(network_code):]
        print(f"剩余部分: {remaining}")
        
        if remaining.startswith(employee_id):
            print(f"✅ 接着匹配业务员工号: {employee_id}")
            remaining = remaining[len(employee_id):]
            print(f"剩余部分: {remaining}")
            
            # 检查设备SN的匹配
            device_sn_part = device_sn[:len(remaining)]
            if remaining.startswith(device_sn[:len(remaining)]):
                print(f"✅ 接着匹配设备SN前缀: {device_sn[:len(remaining)]}")
                print(f"设备SN完整: {device_sn}")
                print(f"Token中设备SN: {remaining}")
                
                # 分析差异
                if len(remaining) == len(device_sn):
                    if remaining == device_sn:
                        print("✅ 设备SN完全匹配")
                    else:
                        print(f"⚠️  设备SN有差异:")
                        print(f"   Token中: {remaining}")
                        print(f"   实际SN:  {device_sn}")
                        print(f"   差异位置: {[i for i in range(len(remaining)) if remaining[i] != device_sn[i]]}")
                else:
                    print(f"⚠️  长度不匹配: Token中{len(remaining)}位 vs 实际{len(device_sn)}位")
    
    print("\n🎯 Token构成规律:")
    print("Token = 网点编号(6位) + 业务员工号(4位) + 设备SN变体(14位)")
    print(f"Token = {network_code} + {employee_id} + {token[10:]}")
    
    # 分析设备SN的变化规律
    token_device_part = token[10:]  # 从第11位开始的14位
    print(f"\n📱 设备SN分析:")
    print(f"原始设备SN: {device_sn}")
    print(f"Token中设备: {token_device_part}")
    
    if token_device_part != device_sn:
        print("🔄 可能的变化规律:")
        print("1. 时间戳相关的变化")
        print("2. 序列号递增")
        print("3. 校验位变化")
        
        # 找出差异位置
        diff_positions = []
        for i in range(min(len(token_device_part), len(device_sn))):
            if token_device_part[i] != device_sn[i]:
                diff_positions.append(i)
        
        if diff_positions:
            print(f"差异位置: {diff_positions}")
            for pos in diff_positions:
                print(f"  位置{pos}: '{device_sn[pos]}' -> '{token_device_part[pos]}'")

def generate_token_variants():
    """生成可能的token变体"""
    
    print("\n🔧 生成Token变体")
    print("-" * 40)
    
    network_code = "530023"
    employee_id = "9528"
    device_sn = "59992231000470"
    
    # 基础token
    base_token = network_code + employee_id + device_sn
    print(f"基础Token: {base_token}")
    
    # 可能的变体
    variants = []
    
    # 变体1: 设备SN最后几位变化
    for i in range(10):
        variant_sn = device_sn[:-1] + str(i)
        variant_token = network_code + employee_id + variant_sn
        variants.append(variant_token)
    
    print(f"\n可能的Token变体 (最后一位变化):")
    for i, variant in enumerate(variants):
        marker = " ✅" if variant == "530023952859992231000473" else ""
        print(f"  {variant}{marker}")

def main():
    """主函数"""
    analyze_token()
    generate_token_variants()
    
    print("\n📋 总结:")
    print("1. Token由三部分组成: 网点编号 + 业务员工号 + 设备SN变体")
    print("2. 设备SN在Token中可能有轻微变化")
    print("3. 变化可能与时间、序列号或校验位相关")

if __name__ == "__main__":
    main()
