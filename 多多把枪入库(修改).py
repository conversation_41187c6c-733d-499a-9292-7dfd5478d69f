import uiautomator2 as u2
import time
import random  # 随机模块

# 连接到当前设备
d = u2.connect()
d.set_input_ime(True)  # 开启快速输入法

try:
    # 读取单号文件
    with open("C:/Users/<USER>/Desktop/多多入库单号.txt", 'r', encoding='utf-8') as f:
        numbers = [line.strip() for line in f.readlines() if line.strip()]  # 读取所有非空单号

    total = len(numbers)  # 计算总单号数量
    success = 0  # 初始化成功处理数量为 0

    # 打乱单号顺序
    random.shuffle(numbers)  # 随机打乱单号列表

    # 开始操作
    for index, line in enumerate(numbers):  # 修正 `lines` 为 `numbers`
        # 显示文件总条数、当前进度以及当前输入的单号
        print(f"总条数: {total}, 当前进度: {index + 1}/{total}, 当前单号: {line}")

        # 将单号输入到指定的输入框
        d(resourceId="com.xunmeng.station:id/et_with_delete", text="运单号").set_text(line)
        d.shell("input keyevent 61")
        time.sleep(0.1)

        # 输入固定手机号码
        d(resourceId="com.xunmeng.station:id/et_with_delete", text="手机号").set_text("13800138000")
	d.shell("input keyevent 61")
        print("输入固定号码")
        time.sleep(0.1)

        # 查找并点击确认框
        d(resourceId="com.xunmeng.station:id/tv_scan_confirm").click()
        success += 1  # 记录成功输入的数量

    print(f"所有单号输入完成！成功处理 {success}/{total} 条。")

finally:
    d.set_input_ime(False)  # 关闭快速输入法
