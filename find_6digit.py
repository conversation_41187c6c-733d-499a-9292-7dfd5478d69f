#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找6位数字
"""

import base64

def find_6digit_numbers():
    """
    从Base64解码结果中寻找6位数字
    """
    encrypted_str = "cucpfo9yhx1mxgd4"
    
    print(f"原始字符串: {encrypted_str}")
    
    # Base64解码
    try:
        decoded = base64.b64decode(encrypted_str)
        print(f"Base64解码结果(hex): {decoded.hex()}")
        print(f"Base64解码结果(bytes): {decoded}")
        
        # 尝试不同的解释方式
        print("\n=== 尝试不同的解释方式 ===")
        
        # 1. 直接转换为整数
        try:
            as_int = int.from_bytes(decoded, byteorder='big')
            print(f"作为大端整数: {as_int}")
        except:
            pass
            
        try:
            as_int = int.from_bytes(decoded, byteorder='little')
            print(f"作为小端整数: {as_int}")
        except:
            pass
        
        # 2. 每4字节一组解释
        print("\n=== 每4字节一组 ===")
        for i in range(0, len(decoded), 4):
            chunk = decoded[i:i+4]
            if len(chunk) == 4:
                big_endian = int.from_bytes(chunk, byteorder='big')
                little_endian = int.from_bytes(chunk, byteorder='little')
                print(f"字节{i}-{i+3}: {chunk.hex()} -> 大端:{big_endian}, 小端:{little_endian}")
        
        # 3. 每2字节一组解释
        print("\n=== 每2字节一组 ===")
        for i in range(0, len(decoded), 2):
            chunk = decoded[i:i+2]
            if len(chunk) == 2:
                big_endian = int.from_bytes(chunk, byteorder='big')
                little_endian = int.from_bytes(chunk, byteorder='little')
                print(f"字节{i}-{i+1}: {chunk.hex()} -> 大端:{big_endian}, 小端:{little_endian}")
        
        # 4. 尝试异或解码
        print("\n=== 异或解码 ===")
        for key in range(256):
            decoded_xor = bytes([b ^ key for b in decoded])
            try:
                decoded_str = decoded_xor.decode('utf-8', errors='ignore')
                # 查找6位数字
                import re
                numbers = re.findall(r'\d{6}', decoded_str)
                if numbers:
                    print(f"异或密钥 {key}: 找到6位数字 {numbers}")
            except:
                pass
        
        # 5. 直接查找数字模式
        print("\n=== 直接查找数字模式 ===")
        decoded_str = decoded.decode('utf-8', errors='ignore')
        import re
        all_numbers = re.findall(r'\d+', decoded_str)
        print(f"所有数字: {all_numbers}")
        
        # 6. 尝试将hex转换为数字
        hex_str = decoded.hex()
        print(f"\n=== Hex字符串分析 ===")
        print(f"Hex字符串: {hex_str}")
        
        # 查找连续的6位数字
        for i in range(len(hex_str) - 5):
            hex_chunk = hex_str[i:i+6]
            try:
                num = int(hex_chunk, 16)
                if 100000 <= num <= 999999:  # 6位数字范围
                    print(f"位置{i}-{i+5}: {hex_chunk} -> {num}")
            except:
                pass
                
    except Exception as e:
        print(f"解码失败: {e}")

if __name__ == "__main__":
    find_6digit_numbers() 