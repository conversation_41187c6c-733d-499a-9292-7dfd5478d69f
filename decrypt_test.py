#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解密测试脚本 - 简化版
基于伪代码分析，尝试解密给定的字符串
"""

import base64
from Crypto.Cipher import AES

def decrypt_string(encrypted_str, key="k301qsjbrh1s6ega"):
    """
    尝试解密字符串
    使用AES-ECB模式和Base64解码
    """
    try:
        print(f"原始字符串: {encrypted_str}")
        print(f"使用密钥: {key}")
        
        # 1. 尝试Base64解码
        try:
            decoded_data = base64.b64decode(encrypted_str)
            print(f"Base64解码成功，长度: {len(decoded_data)}")
            print(f"Base64解码后(hex): {decoded_data.hex()}")
        except Exception as e:
            print(f"Base64解码失败: {e}")
            return None
        
        # 2. AES解密
        cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
        decrypted = cipher.decrypt(decoded_data)
        
        # 3. 去除填充
        decrypted = decrypted.rstrip(b'\0')
        
        print(f"解密后(hex): {decrypted.hex()}")
        print(f"解密后(utf8): {decrypted.decode('utf-8', errors='ignore')}")
        
        return decrypted
        
    except Exception as e:
        print(f"解密失败: {e}")
        return None

def try_simple_analysis(encrypted_str):
    """
    简单分析字符串
    """
    print(f"\n=== 字符串分析 ===")
    print(f"字符串长度: {len(encrypted_str)}")
    
    # ASCII值
    ascii_values = [ord(c) for c in encrypted_str]
    print(f"ASCII值: {ascii_values}")
    
    # 检查是否包含数字
    digits = [c for c in encrypted_str if c.isdigit()]
    print(f"包含的数字: {digits}")
    
    # 检查字符范围
    chars = set(encrypted_str)
    print(f"使用的字符: {sorted(chars)}")

if __name__ == "__main__":
    encrypted_str = "cucpfo9yhx1mxgd4"
    
    print("开始解密测试...")
    print("=" * 50)
    
    # 尝试AES解密
    decrypt_string(encrypted_str)
    
    # 简单分析
    try_simple_analysis(encrypted_str)
    
    print("\n" + "=" * 50)
    print("解密测试完成") 