﻿#Software: 韵达自动化扫描客户端
#ProcessID: 18032 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,277M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-21
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
07:45:45.585 15 Y P KuangScannerNet 准备开始监听4个服务器
07:45:45.596 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
07:45:45.623 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
07:45:45.727 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
07:45:45.745 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
07:45:45.745 15 Y P KuangScannerNet 准备就绪！
07:45:45.763 15 Y P Http 准备开始监听2个服务器
07:45:45.765 15 Y P Http 开始监听 tcp://0.0.0.0:8080
07:45:45.772 15 Y P Http 开始监听 tcp://[::]:8080
07:45:45.772 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 20352 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,209M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-21
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
12:17:59.324  5 Y P KuangScannerNet 准备开始监听4个服务器
12:17:59.469  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
12:17:59.503  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
12:17:59.510  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
12:17:59.516  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
12:17:59.516  5 Y P KuangScannerNet 准备就绪！
12:17:59.535  5 Y P Http 准备开始监听2个服务器
12:17:59.537  5 Y P Http 开始监听 tcp://0.0.0.0:8080
12:17:59.549  5 Y P Http 开始监听 tcp://[::]:8080
12:17:59.549  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 19156 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,218M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32766/1000
#Date: 2024-02-21
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
12:18:33.192  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
12:18:33.194  1 N - NewLife组件核心库 ©2002-2022 NewLife
12:18:33.194  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
12:18:33.194  1 N - 韵达自动化扫描客户端 Copyright ©  2022
12:18:33.194  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
12:18:33.209  1 N - 异常退出！
