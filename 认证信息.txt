认证信息
网点编号: 530023
业务员工号: 9528 (4位数字)
业务员口令: 881000 (88开头6位数字)
分拨中心代码: 530001
巴枪SN码: 59992231000470 (599开头14位数字)

网络通信
服务器: http://scan.yundasys.com:9900/rock
登录接口: /query/wdBqLogin/v1
版本号: 3.6.8.0821
应用类型: wddpyt
加密算法
算法: AES-ECB模式
密钥: k301qsjbrh1s6ega
编码: Base64

express_upload_simulator.py - 主要实现脚本
完整的登录认证功能
AES加密/解密实现
快递单号上传功能
批量处理支持

simple_test.py - 简化测试脚本
验证AES加密解密
测试数据格式
验证关键信息 

express_analysis_guide.md - 详细说明文档
使用方法
技术细节
故障排除

requirements.txt - 依赖包列表



🚀 快速开始
安装依赖:                  pip install -r requirements.txt
运行简单测试:            python simple_test.py
运行完整脚本:            python express_upload_simulator.py
💡 核心功能
登录认证                    uploader = ExpressUploader()
if uploader.login():
    print("登录成功")

数据加密                 encrypted = uploader.aes_encrypt("原始数据")
decrypted = uploader.aes_decrypt(encrypted)


单号上传 uploader.upload_tracking_number("YT1234567890123")






