// 韵达集包的核心处理逻辑

/**
 * 执行韵达集包脚本，处理单号文件
 * @param {string} filePath - 单号文件路径
 * @param {number} startIndex - 开始处理的索引位置，用于断点续传
 */
function runJibaoScript(filePath, startIndex) {
  // 记录文件路径，用于保存处理进度
  var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
  var jl_hs = 0; // 初始化记录行数为0

  // 创建或打开记录文件
  if (files.create(jl_path)) { // 如果文件不存在，创建新文件
    sleep(150); // 等待文件创建完成
  } else { // 如果文件已存在
    try {
      // 读取记录文件的所有行
      var arr_jl = open(jl_path).readlines(); // 读取文件内容
      // 获取最后一行的值作为起始位置，如果文件为空则从0开始
      jl_hs = arr_jl.length === 0 ? 0 : parseInt(arr_jl[arr_jl.length - 1]) + 1;
    } catch (e) {
      // 如果读取文件出错，重新创建文件
      files.remove(jl_path); // 删除可能损坏的文件
      files.create(jl_path); // 创建新文件
      jl_hs = 0; // 重置起始位置为0
    }
  }

  // 确定开始处理的位置：优先使用传入的起始索引，如果没有则使用记录文件中的进度
  var startPos = (startIndex !== undefined && startIndex > 0) ? startIndex : jl_hs;

  // 读取单号文件内容
  var arr_a = open(filePath).readlines(); // 读取单号文件的所有行
  console.log("当前单号文本数量: " + arr_a.length); // 输出单号总数
  console.log("从第 " + (startPos + 1) + " 条开始处理"); // 输出开始处理的位置

  // 循环处理每一行单号
  for (var i = startPos; i < arr_a.length; i++) {
    // 检查是否已暂停，如果暂停则跳出循环
    if (!running) { // 如果脚本已暂停
      // 统一格式的暂停日志输出
      console.warn("当前进度: " + (i + 1) + "/" + arr_a.length); // 输出当前进度
      console.warn("当前单号: " + arr_a[i]); // 输出当前单号
      break; // 跳出循环
    }

    // 更新全局进度变量，以便暂停后可以继续
    currentProgress = i; // 记录当前处理的位置

    // 找到单号输入框并输入当前单号
    var user = id("took_shipment_collection_bill_number").findOne(); // 查找单号输入框
    console.warn("当前单号: " + arr_a[i]); // 输出当前处理的单号，使用黄色高亮显示
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length); // 输出当前进度，使用黄色高亮显示

    // 设置单号并点击添加按钮
    user.setText(arr_a[i]); // 将单号设置到输入框
    sleep(5); // 短暂停，等待UI响应
    id("took_shipment_collection_add").findOne().click(); // 点击添加按钮
    sleep(5); // 短暂停，等待UI响应

    // 处理可能出现的确认对话框
    var cx = text("确定").findOne(135); // 在135毫秒内查找确定按钮
    if (cx !== null) { // 如果找到确定按钮
      cx.click(); // 点击确定按钮
    }

    // 每处理多条记录才写入一次文件，减少频繁IO操作提高性能
    if (i % 1000 === 0 || i === arr_a.length - 1) { // 每1000条或最后一条时写入
      files.append(jl_path, i + "\n"); // 将当前进度追加到记录文件
    }

    // 添加小延时，使暂停检测更可靠，并减轻系统负担
    sleep(5); // 短暂停5毫秒
  }

  // 处理完成后的操作
  // 只有当完成所有单号处理时才显示完成信息
  if (i >= arr_a.length) {
    console.warn("当前进度: " + arr_a.length + "/" + arr_a.length); // 输出完成进度
    console.warn("全部单号处理完成"); // 输出完成信息
  }