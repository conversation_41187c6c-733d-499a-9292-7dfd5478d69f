{"analysis": {"发现的关键信息": {"共同API端点": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", "登录端点": "http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1", "加密方式": "AES-ECB", "加密密钥": "k301qsjbrh1s6ega", "编码方式": "Base64", "版本号": "3.6.8.0821"}, "函数映射": {"sub_401932": {"描述": "主要的网络请求函数", "参数": "6个参数 (a1-a6)", "功能": "构建JSON并发送网络请求", "行号": "507-1225"}, "sub_40332A": {"描述": "时间戳生成函数", "功能": "生成请求时间戳", "行号": "1233-1428"}}, "JSON模板发现": {"位置": "第9654-9655行", "模板": "{\"appType\":\"1\",\"company\":\"[company]\",\"device\":\"[device]\",\"digest\":\"[digest]\",\"password\":\"[password]\",\"reqTime\":\"[reqTime]\",\"uniqueCode\":\"[token]\",\"user\":\"[user]\",\"version\":\"3.6.8.0821\"}"}, "四个菜单业务逻辑": {"1_派件到达": {"业务规则": "固定分拨中心代码530001 + 快递单号", "验证逻辑": "无特殊验证", "推测字段": {"operationType": "delivery_arrival", "distributionCenter": "530001", "trackingNumber": "[快递单号]"}}, "2_到派一体": {"业务规则": "4位数代码 + 快递单号", "验证逻辑": "长度==4验证 (第430行, 8766行)", "推测字段": {"operationType": "dispatch_integration", "stationCode": "[4位数代码]", "trackingNumber": "[快递单号]"}}, "3_乡镇驿站扫描": {"业务规则": "4位数代码 + 快递单号", "验证逻辑": "长度==4验证 (第430行, 8766行)", "推测字段": {"operationType": "rural_station_scan", "stationCode": "[4位数代码]", "trackingNumber": "[快递单号]"}}, "4_集包扫描": {"业务规则": "9开头15位数字 + 下一站530001 + 快递单号", "验证逻辑": "开头==9 && 长度==15", "推测字段": {"operationType": "package_collection", "collectionCode": "[9开头15位数字]", "nextStation": "530001", "trackingNumber": "[快递单号]"}}}, "参数映射分析": {"sub_401932函数参数": {"a1": "用户名/员工ID", "a2": "网络代码/公司代码", "a3": "设备代码", "a4": "密码相关", "a5": "快递单号/扫描码", "a6": "站点代码/分拨中心代码"}}}, "request_examples": {"1_派件到达": {"appType": "1", "company": "[网络代码]", "device": "[设备SN]", "user": "[员工ID]", "password": "[加密后密码]", "digest": "[认证摘要]", "reqTime": "[时间戳]", "uniqueCode": "[token]", "version": "3.6.8.0821", "operationType": "delivery_arrival", "distributionCenter": "530001", "trackingNumber": "312799099115886", "description": "固定使用分拨中心代码530001"}, "2_到派一体": {"appType": "1", "company": "[网络代码]", "device": "[设备SN]", "user": "[员工ID]", "password": "[加密后密码]", "digest": "[认证摘要]", "reqTime": "[时间戳]", "uniqueCode": "[token]", "version": "3.6.8.0821", "operationType": "dispatch_integration", "stationCode": "1234", "trackingNumber": "434640497727147", "description": "需要输入4位数代码进行验证"}, "3_乡镇驿站扫描": {"appType": "1", "company": "[网络代码]", "device": "[设备SN]", "user": "[员工ID]", "password": "[加密后密码]", "digest": "[认证摘要]", "reqTime": "[时间戳]", "uniqueCode": "[token]", "version": "3.6.8.0821", "operationType": "rural_station_scan", "stationCode": "5678", "trackingNumber": "312799099115886", "description": "需要输入4位数代码进行验证"}, "4_集包扫描": {"appType": "1", "company": "[网络代码]", "device": "[设备SN]", "user": "[员工ID]", "password": "[加密后密码]", "digest": "[认证摘要]", "reqTime": "[时间戳]", "uniqueCode": "[token]", "version": "3.6.8.0821", "operationType": "package_collection", "collectionCode": "912345678901234", "nextStation": "530001", "trackingNumber": "434640497727147", "description": "需要9开头15位数字 + 下一站代码530001"}}, "response_examples": {"成功响应": {"code": "200", "message": "操作成功", "data": {"result": "success", "timestamp": "[时间戳]"}}, "验证失败": {"code": "400", "message": "验证失败", "data": {"error": "代码长度不正确"}}, "认证失败": {"code": "401", "message": "认证失败", "data": {"error": "用户名或密码错误"}}}, "encryption_example": {"original_password": "test123", "encrypted_password": "yr+b/daD+Uvt7Hp9F4HbKA==", "encryption_method": "AES-ECB + Base64", "key": "k301qsjbrh1s6ega"}}