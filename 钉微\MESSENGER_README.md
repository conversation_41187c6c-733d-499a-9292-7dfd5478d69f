# 消息转发工具

这是一个用于在钉钉和微信之间自动转发消息的工具。它使用UI自动化技术，可以监控指定的群聊，并根据配置的规则自动转发消息。

## 功能特点

- **双向消息转发**：可以将钉钉消息转发到微信，也可以将微信消息转发到钉钉
- **智能消息筛选**：支持基于关键词、模式识别和自定义规则的消息筛选
- **图形用户界面**：提供简单易用的GUI，方便配置和监控
- **错误恢复机制**：自动检测和恢复连接问题
- **详细日志记录**：记录所有操作和错误，便于排查问题
- **状态监控**：实时显示运行状态和统计信息

## 系统要求

- Windows操作系统
- Python 3.6+
- 已安装并登录的钉钉和微信客户端

## 安装

1. 克隆或下载本仓库
2. 安装依赖：
   ```
   pip install pywinauto
   ```

## 使用方法

### 图形界面模式

直接运行主程序：

```
python messenger.py
```

在图形界面中：
1. 配置钉钉和微信群聊名称
2. 设置消息筛选规则
3. 点击"启动"按钮开始转发
4. 可以在"日志"和"状态"标签页查看运行情况

### 命令行模式

使用`--cli`参数运行：

```
python messenger.py --cli
```

程序将使用配置文件中的设置运行，按Ctrl+C停止。

## 配置说明

配置文件`config.json`包含以下选项：

- `dingtalk_group_name`：钉钉群聊名称
- `wechat_group_name`：微信群聊名称
- `check_interval`：检查新消息的间隔（秒）
- `max_reconnect_attempts`：重连尝试次数
- `reconnect_delay`：重连间隔（秒）
- `log_level`：日志级别
- `keywords`：关键词列表，包含这些关键词的消息会被转发
- `negative_keywords`：负面关键词列表，包含这些关键词的消息不会被转发
- `forward_all_dingtalk`：是否转发所有钉钉消息
- `forward_filtered_wechat`：是否转发筛选后的微信消息
- `message_format`：消息格式模板

## 文件结构

- `messenger.py`：主程序
- `gui.py`：图形界面模块
- `ui_helper.py`：UI操作辅助模块
- `message_filter.py`：消息筛选模块
- `monitor.py`：错误恢复和监控模块
- `config.json`：配置文件

## 注意事项

1. 本工具依赖于UI自动化，如果钉钉或微信客户端更新导致界面变化，可能需要更新代码
2. 使用过程中请不要手动操作钉钉和微信客户端，以免干扰自动化操作
3. 首次使用前，请确保已正确配置群聊名称
4. 如果遇到问题，请查看日志文件`messenger.log`

## 自定义消息筛选规则

可以在`message_filter.py`中添加自定义规则。默认已实现以下规则：

1. 包含关键词的消息会被转发
2. 包含负面关键词的消息不会被转发
3. 包含日期和时间的消息会被转发（可能是会议或截止日期提醒）
4. 包含问号的消息会被转发（可能是问题或请求）
5. 包含感叹号的消息会被转发（可能表示重要性）
6. 工作时间内的消息会被转发（周一至周五 9:00-18:00）
7. 包含"@所有人"的消息会被转发

## 许可证

MIT
