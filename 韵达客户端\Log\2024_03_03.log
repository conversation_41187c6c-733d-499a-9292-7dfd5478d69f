﻿#Software: 韵达自动化扫描客户端
#ProcessID: 12304 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,605M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-03
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:03:23.806  5 Y P KuangScannerNet 准备开始监听4个服务器
06:03:23.817  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:03:23.823  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:03:23.826  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:03:23.892  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:03:23.892  5 Y P KuangScannerNet 准备就绪！
06:03:23.915  5 Y P Http 准备开始监听2个服务器
06:03:23.956  5 Y P Http 开始监听 tcp://0.0.0.0:8080
06:03:23.970  5 Y P Http 开始监听 tcp://[::]:8080
06:03:23.970  5 Y P Http 准备就绪！
06:03:58.034  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
06:03:58.034  1 N - NewLife组件核心库 ©2002-2022 NewLife
06:03:58.034  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
06:03:58.034  1 N - 韵达自动化扫描客户端 Copyright ©  2022
06:03:58.035  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
06:03:58.035  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 12892 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,438M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-03
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:05:30.123 15 Y P KuangScannerNet 准备开始监听4个服务器
06:05:30.191 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:05:30.199 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:05:30.210 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:05:30.214 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:05:30.215 15 Y P KuangScannerNet 准备就绪！
06:05:30.227 15 Y P Http 准备开始监听2个服务器
06:05:30.231 15 Y P Http 开始监听 tcp://0.0.0.0:8080
06:05:30.246 15 Y P Http 开始监听 tcp://[::]:8080
06:05:30.246 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 2428 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,404M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-03
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
08:31:02.419  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
08:31:02.421  1 N - NewLife组件核心库 ©2002-2022 NewLife
08:31:02.421  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
08:31:02.421  1 N - 韵达自动化扫描客户端 Copyright ©  2022
08:31:02.421  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
08:31:02.436  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 15536 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,582M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32765/1000
#Date: 2024-03-03
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
08:34:33.178  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
08:34:33.182  1 N - NewLife组件核心库 ©2002-2022 NewLife
08:34:33.182  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
08:34:33.182  1 N - 韵达自动化扫描客户端 Copyright ©  2022
08:34:33.182  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
08:34:33.290  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 14812 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,710M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-03
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
08:34:45.765 10 Y P KuangScannerNet 准备开始监听4个服务器
08:34:45.873 10 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
08:34:45.875 10 Y P KuangScannerNet 开始监听 tcp://[::]:12345
08:34:45.878 10 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
08:34:45.907 10 Y P KuangScannerNet 开始监听 udp://[::]:12345
08:34:45.907 10 Y P KuangScannerNet 准备就绪！
08:34:45.920 10 Y P Http 准备开始监听2个服务器
08:34:45.941 10 Y P Http 开始监听 tcp://0.0.0.0:8080
08:34:45.941 10 Y P Http 开始监听 tcp://[::]:8080
08:34:45.941 10 Y P Http 准备就绪！
