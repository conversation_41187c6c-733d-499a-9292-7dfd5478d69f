{"analysis_date": "2025-06-21T18:16:06.715795", "title": "韵达物流系统完整分析报告", "summary": "基于IDA伪代码分析的认证信息、网络请求和业务逻辑", "authentication": {"login_endpoint": "http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1", "login_payload": {"appType": "1", "company": "[company]", "device": "[device]", "digest": "[digest]", "password": "[password]", "reqTime": "[reqTime]", "uniqueCode": "[token]", "user": "[user]", "version": "3.6.8.0821"}, "headers": {"token": "[token]", "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)", "Content-Type": "application/octet-stream"}, "response_fields": {"sessionId": "从响应中提取的会话ID"}}, "menu_endpoints": {"1_delivery_arrival": {"name": "派件到达", "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", "method": "POST", "business_rule": "固定设置分拨中心代码 530001", "headers": {"token": "[token]", "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)", "Content-Type": "application/octet-stream"}}, "2_dispatch_integration": {"name": "到派一体", "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", "method": "POST", "business_rule": "单号输入时长度等于4的代码验证", "headers": {"token": "[token]", "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)", "Content-Type": "application/octet-stream"}}, "3_rural_station_scan": {"name": "乡镇驿站扫描", "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", "method": "POST", "business_rule": "单号输入时长度等于4的代码验证", "headers": {"token": "[token]", "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)", "Content-Type": "application/octet-stream"}}, "4_package_collection": {"name": "集包扫描", "endpoint": "http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1", "method": "POST", "business_rule": "单号输入时开头为9的代码 + 下一站为预设6位数代码", "headers": {"token": "[token]", "user-agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)", "Content-Type": "application/octet-stream"}}}, "encryption": {"algorithm": "AES-ECB", "key": "k301qsjbrh1s6ega", "key_length": 16, "encoding": "Base64", "process": "原始数据 → AES-ECB加密 → Base64编码", "pseudocode_locations": {"key_definition": "第9756行", "algorithm_specification": "第9758行", "encryption_function": "第9759行 sub_404606", "base64_function": "第9768行 sub_406AF8"}}, "business_rules": {"delivery_arrival": {"rule": "派件到达时固定设置分拨中心代码为530001", "validation": "distribution_center_code == '530001'"}, "dispatch_integration": {"rule": "到派一体操作时验证输入代码长度为4位数字", "validation": "len(input_code) == 4 and input_code.isdigit()"}, "rural_station_scan": {"rule": "乡镇驿站扫描时验证输入代码长度为4位数字", "validation": "len(input_code) == 4 and input_code.isdigit()"}, "package_collection": {"rule": "集包扫描时验证单号以9开头且下一站代码为6位数字", "validation": "tracking_number.startswith('9') and len(next_station) == 6 and next_station.isdigit()"}}, "test_data": {"real_tracking_numbers": ["312799099115886", "434640497727147"], "analysis": "这些15位快递单号不符合集包扫描的'以9开头'规则，说明集包扫描针对的是特定的内部代码格式", "sample_encrypted_data": "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQ...", "data_source": "乡镇驿站扫描抓包数据"}, "technical_details": {"server_info": {"domain": "scan.yundasys.com", "port": 9900, "protocol": "HTTP"}, "api_structure": {"base_path": "/rock", "login_path": "/rock/query/wdBqLogin/v1", "scan_path": "/rock/upload/outlet/scan/bq/v1"}, "version_info": {"app_version": "3.6.8.0821", "app_type": "1"}}, "security_analysis": {"encryption_strength": "AES-128-ECB (相对较弱，ECB模式不推荐)", "authentication_method": "基于token和digest的认证", "potential_vulnerabilities": ["ECB模式可能泄露数据模式", "HTTP协议未加密传输", "固定的User-Agent可能被检测"], "recommendations": ["升级到AES-CBC或AES-GCM模式", "使用HTTPS协议", "实现更强的身份验证机制"]}}