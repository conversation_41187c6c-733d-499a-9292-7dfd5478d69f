import hashlib
import base64
import json
import time

def generate_digest(data):
    """
    生成正确的digest签名
    
    算法: 将company+user+password+device+reqTime+data字段按顺序拼接，
    计算MD5哈希值，然后进行base64编码
    
    参数:
        data (dict): 包含请求参数的字典
        
    返回:
        str: 生成的digest签名
    """
    # 按照顺序拼接字段
    data_str = (
        f"{data['company']}"
        f"{data['user']}"
        f"{data['password']}"
        f"{data['device']}"
        f"{data['reqTime']}"
        f"{data['data']}"
    )
    
    # 计算MD5
    md5_hash = hashlib.md5(data_str.encode()).hexdigest()
    
    # 转换为base64
    digest = base64.b64encode(md5_hash.encode()).decode()
    
    return digest

def create_request_data(company, user, password, device, data_value, version="3.6.8.0821"):
    """
    创建包含正确digest的请求数据
    
    参数:
        company (str): 公司代码
        user (str): 用户名
        password (str): 密码的MD5哈希值
        device (str): 设备ID
        data_value (str): 数据值
        version (str): 版本号，默认为"3.6.8.0821"
        
    返回:
        dict: 包含所有必要字段和正确digest的请求数据
    """
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    data = {
        "brandCode": None,
        "company": company,
        "data": data_value,
        "device": device,
        "password": password,
        "reqTime": current_time,
        "status": None,
        "updateTime": None,
        "user": user,
        "version": version
    }
    
    # 生成并添加digest
    data["digest"] = generate_digest(data)
    
    return data

if __name__ == "__main__":
    # 示例用法
    company = "886209"
    user = "1111"
    password = "4297f44b13955235245b2497399d7a93"  # 这是"123456"的MD5值
    device = "51112315001747"
    data_value = "434542754941704"
    
    request_data = create_request_data(company, user, password, device, data_value)
    
    print("生成的请求数据:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    print("\ndigest值:")
    print(request_data["digest"])
