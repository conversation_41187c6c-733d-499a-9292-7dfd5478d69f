import requests
import json
import time
from digest_generator import create_request_data

def make_api_request(company, user, password, device, data_value, version="3.6.8.0821"):
    """
    使用正确的digest签名发送API请求
    
    参数:
        company (str): 公司代码
        user (str): 用户名
        password (str): 密码的MD5哈希值
        device (str): 设备ID
        data_value (str): 数据值
        version (str): 版本号，默认为"3.6.8.0821"
        
    返回:
        dict: API响应的JSON数据
    """
    url = "http://scan.yundasys.com:9900/rock/query/waybillMarkingSearch/v1"
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # 创建带有正确digest的请求数据
    data = create_request_data(company, user, password, device, data_value, version)
    
    print("发送请求:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    
    # 发送请求
    response = requests.post(url, json=data, headers=headers)
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    return response.json()

if __name__ == "__main__":
    # 示例用法
    company = "886209"
    user = "1111"
    password = "4297f44b13955235245b2497399d7a93"  # 这是"123456"的MD5值
    device = "51112315001747"
    data_value = "434542754941704"
    
    print("=== 使用破解的digest算法发送API请求 ===")
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    result = make_api_request(company, user, password, device, data_value)
    
    if result.get("code") == 0:
        print("\n请求成功!")
        print(f"返回数据: {result.get('data')}")
    else:
        print("\n请求失败!")
        print(f"错误信息: {result.get('msg')}")
