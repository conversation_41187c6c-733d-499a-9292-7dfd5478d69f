#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
尝试解码6位数字
"""

import base64
import hashlib

def try_decode_6digit():
    """
    尝试各种方法解码出6位数字
    """
    encrypted_str = "cucpfo9yhx1mxgd4"
    
    print(f"原始字符串: {encrypted_str}")
    print("=" * 50)
    
    # 方法1: 检查是否是MD5哈希
    print("=== 方法1: MD5哈希检查 ===")
    # 尝试常见的6位数字作为MD5输入
    for i in range(100000, 1000000):
        if i % 10000 == 0:  # 每10000个打印一次进度
            print(f"检查进度: {i}")
        test_str = str(i)
        md5_hash = hashlib.md5(test_str.encode()).hexdigest()
        if md5_hash.startswith(encrypted_str[:8]):  # 检查前8位
            print(f"找到匹配! 数字: {test_str}, MD5: {md5_hash}")
            break
    
    # 方法2: 检查是否是Base64编码的6位数字
    print("\n=== 方法2: Base64编码检查 ===")
    for i in range(100000, 1000000):
        if i % 10000 == 0:
            print(f"检查进度: {i}")
        test_str = str(i)
        b64_encoded = base64.b64encode(test_str.encode()).decode()
        if b64_encoded == encrypted_str:
            print(f"找到匹配! 数字: {test_str}")
            break
    
    # 方法3: 检查是否是某种自定义编码
    print("\n=== 方法3: 自定义编码检查 ===")
    # 尝试简单的字符替换
    char_map = {
        'c': '0', 'u': '1', 'p': '2', 'f': '3', 'o': '4', 
        '9': '5', 'y': '6', 'h': '7', 'x': '8', '1': '9',
        'm': '0', 'g': '1', 'd': '2', '4': '3'
    }
    
    decoded = ""
    for c in encrypted_str:
        if c in char_map:
            decoded += char_map[c]
        else:
            decoded += c
    
    print(f"字符映射结果: {decoded}")
    
    # 方法4: 检查是否是异或编码
    print("\n=== 方法4: 异或编码检查 ===")
    for key in range(256):
        decoded_bytes = bytes([ord(c) ^ key for c in encrypted_str])
        try:
            decoded_str = decoded_bytes.decode('utf-8')
            if decoded_str.isdigit() and len(decoded_str) == 6:
                print(f"异或密钥 {key}: {decoded_str}")
        except:
            pass
    
    # 方法5: 检查是否是某种位移编码
    print("\n=== 方法5: 位移编码检查 ===")
    for shift in range(1, 26):
        decoded = ""
        for c in encrypted_str:
            if c.isalpha():
                # 字母位移
                if c.islower():
                    new_char = chr((ord(c) - ord('a') + shift) % 26 + ord('a'))
                else:
                    new_char = chr((ord(c) - ord('A') + shift) % 26 + ord('A'))
                decoded += new_char
            else:
                decoded += c
        print(f"位移 {shift}: {decoded}")

if __name__ == "__main__":
    try_decode_6digit() 