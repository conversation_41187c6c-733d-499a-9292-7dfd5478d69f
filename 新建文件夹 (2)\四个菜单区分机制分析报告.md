# 四个菜单区分机制分析报告

## 📋 概述

基于IDA伪代码分析，成功识别了韵达物流系统中四个菜单功能的区分机制和网络请求格式。

## 🔍 关键发现

### 1. 共同技术架构
- **API端点**: `http://scan.yundasys.com:9900/rock/upload/outlet/scan/bq/v1`
- **登录端点**: `http://scan.yundasys.com:9900/rock/query/wdBqLogin/v1`
- **加密方式**: AES-ECB + Base64编码
- **加密密钥**: `k301qsjbrh1s6ega`
- **应用版本**: `3.6.8.0821`

### 2. 核心函数分析
- **sub_401932**: 主要网络请求函数 (第507-1225行)
- **sub_40332A**: 时间戳生成函数 (第1233-1428行)
- **JSON模板**: 第9654-9655行发现完整JSON结构

### 3. 四个菜单业务逻辑

#### 🚚 1. 派件到达
- **业务规则**: 固定分拨中心代码530001 + 快递单号
- **验证逻辑**: 无特殊验证
- **请求字段**:
  ```json
  {
    "operationType": "delivery_arrival",
    "distributionCenter": "530001",
    "trackingNumber": "[快递单号]"
  }
  ```

#### 📦 2. 到派一体
- **业务规则**: 4位数代码 + 快递单号
- **验证逻辑**: 长度==4验证 (伪代码第430行, 8766行)
- **请求字段**:
  ```json
  {
    "operationType": "dispatch_integration",
    "stationCode": "[4位数代码]",
    "trackingNumber": "[快递单号]"
  }
  ```

#### 🏘️ 3. 乡镇驿站扫描
- **业务规则**: 4位数代码 + 快递单号
- **验证逻辑**: 长度==4验证 (伪代码第430行, 8766行)
- **请求字段**:
  ```json
  {
    "operationType": "rural_station_scan",
    "stationCode": "[4位数代码]",
    "trackingNumber": "[快递单号]"
  }
  ```

#### 📋 4. 集包扫描
- **业务规则**: 9开头15位数字 + 下一站530001 + 快递单号
- **验证逻辑**: 开头==9 && 长度==15
- **请求字段**:
  ```json
  {
    "operationType": "package_collection",
    "collectionCode": "[9开头15位数字]",
    "nextStation": "530001",
    "trackingNumber": "[快递单号]"
  }
  ```

## 🔐 认证机制

### 基础JSON结构
```json
{
  "appType": "1",
  "company": "[网络代码]",
  "device": "[设备SN]",
  "user": "[员工ID]",
  "password": "[AES加密后密码]",
  "digest": "[认证摘要]",
  "reqTime": "[时间戳]",
  "uniqueCode": "[token]",
  "version": "3.6.8.0821"
}
```

### 认证格式
**网络代码 + 员工ID + 密码 + 分拨中心代码 + 设备SN**

### 密码加密示例
- **原始密码**: `test123`
- **加密结果**: `yr+b/daD+Uvt7Hp9F4HbKA==`
- **加密方法**: AES-ECB + Base64
- **密钥**: `k301qsjbrh1s6ega`

## 📊 请求示例

### 派件到达请求
```json
{
  "appType": "1",
  "company": "[网络代码]",
  "device": "[设备SN]",
  "user": "[员工ID]",
  "password": "[加密后密码]",
  "digest": "[认证摘要]",
  "reqTime": "[时间戳]",
  "uniqueCode": "[token]",
  "version": "3.6.8.0821",
  "operationType": "delivery_arrival",
  "distributionCenter": "530001",
  "trackingNumber": "312799099115886"
}
```

### 集包扫描请求
```json
{
  "appType": "1",
  "company": "[网络代码]",
  "device": "[设备SN]",
  "user": "[员工ID]",
  "password": "[加密后密码]",
  "digest": "[认证摘要]",
  "reqTime": "[时间戳]",
  "uniqueCode": "[token]",
  "version": "3.6.8.0821",
  "operationType": "package_collection",
  "collectionCode": "912345678901234",
  "nextStation": "530001",
  "trackingNumber": "434640497727147"
}
```

## 📈 响应格式

### 成功响应
```json
{
  "code": "200",
  "message": "操作成功",
  "data": {
    "result": "success",
    "timestamp": "[时间戳]"
  }
}
```

### 验证失败
```json
{
  "code": "400",
  "message": "验证失败",
  "data": {
    "error": "代码长度不正确"
  }
}
```

## 🎯 核心结论

1. **统一端点**: 四个菜单共用同一个API端点
2. **字段区分**: 通过JSON载荷中的不同字段进行功能区分
3. **业务验证**: 每个菜单有特定的输入验证规则
4. **加密安全**: 使用AES-ECB加密保护敏感信息
5. **测试单号**: 312799099115886, 434640497727147

## 📁 生成文件

- `complete_menu_analysis.json`: 完整分析结果
- `complete_menu_analysis.py`: 分析脚本
- `四个菜单区分机制分析报告.md`: 本报告

---

**分析完成时间**: 2025-06-21  
**分析方法**: IDA伪代码静态分析  
**关键发现**: 成功识别四个菜单的区分机制和完整请求格式
