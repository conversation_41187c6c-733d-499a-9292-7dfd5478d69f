import logging
import json
import time
import tkinter as tk
import threading
import sys
import os
from ui_helper import UIHelper
from message_filter import MessageFilter
from monitor import Monitor
from gui import MessengerGUI

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('messenger.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Messenger:
    """消息转发工具主类"""
    
    def __init__(self, config_file='config.json'):
        """初始化消息转发工具"""
        self.config_file = config_file
        self.config = self._load_config()
        
        # 初始化组件
        self.ui_helper = UIHelper()
        self.message_filter = MessageFilter(config_file)
        self.monitor = Monitor(config_file)
        
        # 消息记录，避免重复处理
        self.processed_dingtalk_messages = set()
        self.processed_wechat_messages = set()
        
        # 应用窗口
        self.dingtalk_window = None
        self.wechat_window = None
        
        # 运行状态
        self.running = False
        self.worker_thread = None
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"配置文件 {self.config_file} 不存在，将使用默认配置")
                return {
                    'dingtalk_group_name': '您的钉钉群名称',
                    'wechat_group_name': '您的微信群名称',
                    'check_interval': 5,
                    'forward_all_dingtalk': True,
                    'forward_filtered_wechat': True
                }
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {
                'dingtalk_group_name': '您的钉钉群名称',
                'wechat_group_name': '您的微信群名称',
                'check_interval': 5,
                'forward_all_dingtalk': True,
                'forward_filtered_wechat': True
            }
    
    def connect_to_apps(self):
        """连接到钉钉和微信应用"""
        try:
            # 连接到钉钉
            _, dingtalk_window = self.ui_helper.connect_app("钉钉")
            self.dingtalk_window = dingtalk_window
            self.monitor.update_status('dingtalk_connected', True)
            
            # 连接到微信
            _, wechat_window = self.ui_helper.connect_app("微信")
            self.wechat_window = wechat_window
            self.monitor.update_status('wechat_connected', True)
            
            logger.info("成功连接到钉钉和微信")
            return True
        except Exception as e:
            logger.error(f"连接应用失败: {e}")
            self.monitor.record_error(f"连接应用失败: {e}")
            return False
    
    def start(self):
        """启动消息转发"""
        if self.running:
            return
        
        # 重新加载配置
        self.config = self._load_config()
        
        # 连接到应用
        if not self.connect_to_apps():
            logger.error("无法连接到应用，程序无法启动")
            return False
        
        # 启动工作线程
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_thread)
        self.worker_thread.daemon = True
        self.worker_thread.start()
        
        logger.info("消息转发已启动")
        return True
    
    def stop(self):
        """停止消息转发"""
        if not self.running:
            return
        
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=2)
        
        # 停止监控
        self.monitor.stop()
        
        logger.info("消息转发已停止")
    
    def _worker_thread(self):
        """工作线程函数"""
        dingtalk_group_name = self.config.get('dingtalk_group_name')
        wechat_group_name = self.config.get('wechat_group_name')
        check_interval = self.config.get('check_interval', 5)
        
        logger.info(f"开始监控群聊: 钉钉[{dingtalk_group_name}], 微信[{wechat_group_name}]")
        
        try:
            while self.running:
                try:
                    # 获取钉钉最新消息并转发到微信
                    if self.config.get('forward_all_dingtalk', True):
                        self._forward_dingtalk_to_wechat(dingtalk_group_name, wechat_group_name)
                    
                    # 获取微信最新消息并有条件地转发到钉钉
                    if self.config.get('forward_filtered_wechat', True):
                        self._forward_wechat_to_dingtalk(wechat_group_name, dingtalk_group_name)
                    
                    # 等待一段时间再检查新消息
                    time.sleep(check_interval)
                except Exception as e:
                    logger.error(f"消息转发过程中出错: {e}")
                    self.monitor.record_error(f"消息转发过程中出错: {e}")
                    
                    # 尝试重新连接
                    if not self.dingtalk_window or not self.wechat_window:
                        logger.info("尝试重新连接应用...")
                        self.connect_to_apps()
                    
                    # 暂停一段时间后继续
                    time.sleep(check_interval)
        except Exception as e:
            logger.error(f"工作线程异常: {e}")
            self.monitor.record_error(f"工作线程异常: {e}")
        finally:
            logger.info("工作线程结束")
    
    def _forward_dingtalk_to_wechat(self, dingtalk_group_name, wechat_group_name):
        """将钉钉消息转发到微信"""
        try:
            # 检查钉钉连接
            if not self.dingtalk_window:
                logger.warning("钉钉窗口未连接，尝试重新连接")
                _, self.dingtalk_window = self.ui_helper.connect_app("钉钉")
                if not self.dingtalk_window:
                    return
            
            # 检查微信连接
            if not self.wechat_window:
                logger.warning("微信窗口未连接，尝试重新连接")
                _, self.wechat_window = self.ui_helper.connect_app("微信")
                if not self.wechat_window:
                    return
            
            # 获取钉钉最新消息
            dingtalk_messages = self.ui_helper.get_messages(
                self.dingtalk_window, 
                dingtalk_group_name, 
                "dingtalk", 
                self.processed_dingtalk_messages
            )
            
            # 转发消息到微信
            for message in dingtalk_messages:
                # 格式化消息
                formatted_message = self.message_filter.format_message(
                    message['content'], 
                    message['sender'], 
                    'dingtalk'
                )
                
                # 发送到微信
                if self.ui_helper.send_message(
                    self.wechat_window, 
                    wechat_group_name, 
                    formatted_message, 
                    "微信"
                ):
                    logger.info(f"已转发钉钉消息到微信: {formatted_message[:30]}...")
                    self.monitor.update_status('dingtalk_message_count', 1)
                    self.monitor.update_status('last_dingtalk_message', {
                        'sender': message['sender'],
                        'content': message['content'][:50] + ('...' if len(message['content']) > 50 else ''),
                        'time': time.strftime('%Y-%m-%d %H:%M:%S')
                    })
        except Exception as e:
            logger.error(f"转发钉钉消息到微信失败: {e}")
            self.monitor.record_error(f"转发钉钉消息到微信失败: {e}")
    
    def _forward_wechat_to_dingtalk(self, wechat_group_name, dingtalk_group_name):
        """将微信消息转发到钉钉"""
        try:
            # 检查微信连接
            if not self.wechat_window:
                logger.warning("微信窗口未连接，尝试重新连接")
                _, self.wechat_window = self.ui_helper.connect_app("微信")
                if not self.wechat_window:
                    return
            
            # 检查钉钉连接
            if not self.dingtalk_window:
                logger.warning("钉钉窗口未连接，尝试重新连接")
                _, self.dingtalk_window = self.ui_helper.connect_app("钉钉")
                if not self.dingtalk_window:
                    return
            
            # 获取微信最新消息
            wechat_messages = self.ui_helper.get_messages(
                self.wechat_window, 
                wechat_group_name, 
                "wechat", 
                self.processed_wechat_messages
            )
            
            # 有条件地转发消息到钉钉
            for message in wechat_messages:
                # 应用筛选条件
                if self.message_filter.should_forward(message['content'], message['sender'], 'wechat'):
                    # 格式化消息
                    formatted_message = self.message_filter.format_message(
                        message['content'], 
                        message['sender'], 
                        'wechat'
                    )
                    
                    # 发送到钉钉
                    if self.ui_helper.send_message(
                        self.dingtalk_window, 
                        dingtalk_group_name, 
                        formatted_message, 
                        "钉钉"
                    ):
                        logger.info(f"已转发微信消息到钉钉: {formatted_message[:30]}...")
                        self.monitor.update_status('wechat_message_count', 1)
                        self.monitor.update_status('last_wechat_message', {
                            'sender': message['sender'],
                            'content': message['content'][:50] + ('...' if len(message['content']) > 50 else ''),
                            'time': time.strftime('%Y-%m-%d %H:%M:%S')
                        })
        except Exception as e:
            logger.error(f"转发微信消息到钉钉失败: {e}")
            self.monitor.record_error(f"转发微信消息到钉钉失败: {e}")

def run_gui():
    """运行GUI界面"""
    root = tk.Tk()
    messenger = Messenger()
    
    def start_callback():
        return messenger.start()
    
    def stop_callback():
        messenger.stop()
    
    app = MessengerGUI(root, start_callback, stop_callback)
    root.mainloop()

def run_cli():
    """运行命令行界面"""
    messenger = Messenger()
    
    try:
        logger.info("启动消息转发工具...")
        if messenger.start():
            logger.info("消息转发工具已启动，按Ctrl+C停止")
            try:
                while messenger.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("接收到停止信号")
            finally:
                messenger.stop()
        else:
            logger.error("启动失败")
    except Exception as e:
        logger.error(f"运行出错: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    # 检查是否有命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        run_cli()
    else:
        run_gui()
