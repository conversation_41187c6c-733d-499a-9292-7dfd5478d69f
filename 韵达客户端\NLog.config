﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" autoReload="true" internalLogFile="internallog.txt" throwExceptions="false">

  <extensions>
  </extensions>

  <targets async="true">
    <target name="file" xsi:type="File"
            archiveAboveSize="10240000"
            maxArchiveFiles="45"
            archiveNumbering="Sequence"
            archiveEvery="Day"
            archiveFileName="${basedir}/Log/GpArchive/log.{#}.txt"
            archiveDateFormat="yyyy-MM-dd-HH-mm"
            createDirs="true"
            bufferSize="10240"
            autoFlush="true"
            fileName="${basedir}/Log/log.txt"
            layout="${longdate}|${level}|threadid=${threadid}|${message}" />
    />
  </targets>


  <rules>
    <logger name="*" minlevel="Trace" writeTo="file"/>
  </rules>
</nlog>