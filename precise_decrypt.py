#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确解密脚本 - 基于检测到的JSON特征进行精确解密
"""

import base64
import json
import hashlib
from Crypto.Cipher import AES

def try_decode_with_different_encodings(data):
    """尝试不同的编码方式解码数据"""
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'gbk', 'big5']
    
    for encoding in encodings:
        try:
            decoded = data.decode(encoding)
            # 检查是否包含JSON特征
            if '{' in decoded and '"' in decoded:
                # 计算可打印字符比例
                printable_count = sum(1 for c in decoded if c.isprintable() or c in '\n\r\t')
                printable_ratio = printable_count / len(decoded)
                
                if printable_ratio > 0.7:  # 70%以上可打印字符
                    print(f"✓ {encoding} 编码成功!")
                    print(f"  可打印字符比例: {printable_ratio:.2%}")
                    print(f"  前200字符: {decoded[:200]}")
                    
                    # 尝试解析JSON
                    try:
                        # 寻找JSON的开始和结束
                        start_idx = decoded.find('{')
                        if start_idx != -1:
                            # 从第一个{开始寻找完整的JSON
                            json_part = decoded[start_idx:]
                            
                            # 尝试解析
                            json_data = json.loads(json_part)
                            print(f"  ✓ JSON解析成功!")
                            return decoded, json_data
                    except json.JSONDecodeError as e:
                        print(f"  - JSON解析失败: {e}")
                        # 尝试修复常见的JSON问题
                        try:
                            # 移除可能的填充字符
                            cleaned = decoded.strip('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f')
                            if '{' in cleaned:
                                start_idx = cleaned.find('{')
                                json_part = cleaned[start_idx:]
                                json_data = json.loads(json_part)
                                print(f"  ✓ 清理后JSON解析成功!")
                                return cleaned, json_data
                        except:
                            pass
                    
                    return decoded, None
        except UnicodeDecodeError:
            continue
    
    return None, None

def analyze_hex_patterns(data):
    """分析十六进制数据中的模式"""
    hex_str = data.hex()
    
    print("=== 十六进制模式分析 ===")
    print(f"数据长度: {len(data)} 字节")
    
    # 查找JSON相关的十六进制模式
    json_patterns = {
        '7b': '{',
        '7d': '}', 
        '22': '"',
        '3a': ':',
        '2c': ',',
        '5b': '[',
        '5d': ']'
    }
    
    found_patterns = {}
    for hex_pattern, char in json_patterns.items():
        positions = []
        start = 0
        while True:
            pos = hex_str.find(hex_pattern, start)
            if pos == -1:
                break
            positions.append(pos // 2)  # 转换为字节位置
            start = pos + 2
        
        if positions:
            found_patterns[char] = positions[:10]  # 只显示前10个位置
    
    print("发现的JSON字符位置:")
    for char, positions in found_patterns.items():
        print(f"  '{char}': {positions}")
    
    # 尝试提取可能的JSON部分
    if '{' in [char for char in found_patterns.keys()]:
        first_brace = found_patterns['{'][0] if '{' in found_patterns else 0
        print(f"\n从第一个 '{{' 开始的数据 (位置 {first_brace}):")
        json_candidate = data[first_brace:]
        print(f"十六进制: {json_candidate[:64].hex()}")
        
        # 尝试解码这部分数据
        text, json_data = try_decode_with_different_encodings(json_candidate)
        if text:
            return text, json_data
    
    return None, None

def main():
    """主函数"""
    print("精确解密分析 - 基于JSON特征检测")
    print("="*50)
    
    # 加密数据
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    # Base64解码
    encrypted_bytes = base64.b64decode(encrypted_data)
    
    # 使用最有希望的密钥进行解密
    promising_keys = [
        ("原始密钥", "k301qsjbrh1s6ega"),
        ("MD5哈希", hashlib.md5("k301qsjbrh1s6ega".encode()).hexdigest()[:16]),
        ("反转密钥", "age6s1hrbjsq103k"),
        ("530001组合", "530001k301qsjb"),
        ("系统密钥", "yundasys123456"),
    ]
    
    for key_name, key in promising_keys:
        print(f"\n=== 尝试 {key_name}: {key} ===")
        
        try:
            # AES解密
            key_bytes = key.encode('utf-8')[:16].ljust(16, b'\x00')
            cipher = AES.new(key_bytes, AES.MODE_ECB)
            decrypted = cipher.decrypt(encrypted_bytes)
            
            print(f"解密后数据长度: {len(decrypted)} 字节")
            
            # 分析十六进制模式
            text, json_data = analyze_hex_patterns(decrypted)
            
            if text:
                print(f"\n🎉 {key_name} 解密成功!")
                print(f"解密结果长度: {len(text)}")
                
                # 保存结果
                with open(f'success_{key_name.replace(" ", "_")}.txt', 'w', encoding='utf-8') as f:
                    f.write(f"密钥: {key}\n")
                    f.write(f"解密结果:\n{text}")
                
                if json_data:
                    print(f"JSON字段: {list(json_data.keys())}")
                    with open(f'success_{key_name.replace(" ", "_")}.json', 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, indent=2, ensure_ascii=False)
                    
                    print("\nJSON内容:")
                    print(json.dumps(json_data, indent=2, ensure_ascii=False))
                
                return  # 成功后退出
                
        except Exception as e:
            print(f"解密失败: {e}")
    
    print("\n❌ 所有尝试都失败了")
    print("建议:")
    print("1. 检查是否使用了CBC模式（需要IV）")
    print("2. 可能有额外的密钥派生函数")
    print("3. 数据可能有多层加密")

if __name__ == "__main__":
    main()
