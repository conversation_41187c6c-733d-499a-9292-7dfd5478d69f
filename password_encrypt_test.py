#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码加密测试脚本
"""

import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

def encrypt_password(password, key="k301qsjbrh1s6ega"):
    """使用AES-ECB + Base64加密密码"""
    try:
        # AES-ECB加密
        cipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
        padded_password = pad(password.encode('utf-8'), AES.block_size)
        encrypted = cipher.encrypt(padded_password)
        # Base64编码
        return base64.b64encode(encrypted).decode('utf-8')
    except Exception as e:
        return f"[加密失败: {e}]"

def main():
    """测试不同密码的加密结果"""
    
    print("🔐 密码加密测试")
    print("=" * 50)
    
    # 测试密码列表
    test_passwords = [
        "test123",  # 原有示例
        "88110",    # 用户询问的密码
        "123456",   # 常见密码
        "admin",    # 常见密码
        "password"  # 常见密码
    ]
    
    key = "k301qsjbrh1s6ega"
    
    print(f"加密密钥: {key}")
    print(f"加密方法: AES-ECB + Base64")
    print("-" * 50)
    
    for password in test_passwords:
        encrypted = encrypt_password(password, key)
        print(f"原始密码: {password:10} -> 加密结果: {encrypted}")
    
    print("-" * 50)
    print("✅ 加密测试完成")

if __name__ == "__main__":
    main()
