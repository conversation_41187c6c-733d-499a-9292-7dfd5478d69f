import pywinauto
import time
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 消息记录，避免重复处理
processed_dingtalk_messages = set()
processed_wechat_messages = set()

# 消息筛选条件（可以根据需要修改）
def should_forward_wechat_message(message, sender):
    # 示例：只转发包含特定关键词的消息
    keywords = ["重要", "紧急", "请回复", "通知"]
    return any(keyword in message for keyword in keywords)

def connect_to_apps():
    """连接到钉钉和微信应用"""
    try:
        # 连接到钉钉
        dingtalk = pywinauto.Application(backend="uia").connect(title_re="钉钉")
        dingtalk_window = dingtalk.window(title_re="钉钉")
        
        # 连接到微信
        wechat = pywinauto.Application(backend="uia").connect(title_re="微信")
        wechat_window = wechat.window(title_re="微信")
        
        return dingtalk_window, wechat_window
    except Exception as e:
        logger.error(f"连接应用失败: {e}")
        return None, None

def get_dingtalk_messages(dingtalk_window, group_name):
    """获取钉钉群的最新消息"""
    try:
        # 确保钉钉窗口处于活动状态
        dingtalk_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = dingtalk_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 获取消息区域
        message_area = dingtalk_window.child_window(control_type="Custom", class_name="conversation-message-list")
        
        # 获取所有消息元素
        message_elements = message_area.children(control_type="Text")
        
        # 提取消息内容和发送者
        messages = []
        for i in range(len(message_elements) - 1, -1, -1):
            try:
                # 尝试获取消息内容
                message_text = message_elements[i].window_text()
                
                # 尝试获取发送者（可能在相邻元素中）
                sender = "未知用户"
                if i > 0:
                    sender_element = message_elements[i-1]
                    if ":" not in sender_element.window_text():
                        sender = sender_element.window_text()
                
                # 创建消息标识符（发送者+消息内容）
                message_id = f"{sender}:{message_text}"
                
                # 如果是新消息，添加到结果中
                if message_id not in processed_dingtalk_messages:
                    messages.append({
                        "sender": sender,
                        "content": message_text,
                        "id": message_id
                    })
                    processed_dingtalk_messages.add(message_id)
            except Exception as e:
                logger.warning(f"处理钉钉消息元素时出错: {e}")
        
        return messages
    except Exception as e:
        logger.error(f"获取钉钉消息失败: {e}")
        return []

def send_to_wechat(wechat_window, group_name, message):
    """发送消息到微信群"""
    try:
        # 确保微信窗口处于活动状态
        wechat_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = wechat_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 找到输入框
        input_box = wechat_window.child_window(control_type="Edit")
        input_box.set_focus()
        
        # 清空输入框
        input_box.type_keys("^a{BACKSPACE}")
        
        # 输入消息并发送
        input_box.type_keys(message, with_spaces=True)
        input_box.type_keys("{ENTER}")
        
        logger.info(f"已发送消息到微信群: {message}")
        return True
    except Exception as e:
        logger.error(f"发送微信消息失败: {e}")
        return False

def get_wechat_messages(wechat_window, group_name):
    """获取微信群的最新消息"""
    try:
        # 确保微信窗口处于活动状态
        wechat_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = wechat_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 获取消息区域
        message_area = wechat_window.child_window(control_type="List", class_name="MessageList")
        
        # 获取所有消息元素
        message_elements = message_area.children(control_type="ListItem")
        
        # 提取消息内容和发送者
        messages = []
        for element in message_elements:
            try:
                # 尝试获取发送者和消息内容
                sender_element = element.child_window(control_type="Text", found_index=0)
                content_element = element.child_window(control_type="Text", found_index=1)
                
                sender = sender_element.window_text()
                content = content_element.window_text()
                
                # 创建消息标识符
                message_id = f"{sender}:{content}"
                
                # 如果是新消息，添加到结果中
                if message_id not in processed_wechat_messages:
                    messages.append({
                        "sender": sender,
                        "content": content,
                        "id": message_id
                    })
                    processed_wechat_messages.add(message_id)
            except Exception as e:
                logger.warning(f"处理微信消息元素时出错: {e}")
        
        return messages
    except Exception as e:
        logger.error(f"获取微信消息失败: {e}")
        return []

def send_to_dingtalk(dingtalk_window, group_name, message):
    """发送消息到钉钉群"""
    try:
        # 确保钉钉窗口处于活动状态
        dingtalk_window.set_focus()
        
        # 找到并点击目标群聊
        chat_list = dingtalk_window.child_window(control_type="List")
        target_group = chat_list.child_window(title=group_name, control_type="ListItem")
        target_group.click_input()
        time.sleep(1)
        
        # 找到输入框
        input_box = dingtalk_window.child_window(control_type="Edit")
        input_box.set_focus()
        
        # 清空输入框
        input_box.type_keys("^a{BACKSPACE}")
        
        # 输入消息并发送
        input_box.type_keys(message, with_spaces=True)
        input_box.type_keys("{ENTER}")
        
        logger.info(f"已发送消息到钉钉群: {message}")
        return True
    except Exception as e:
        logger.error(f"发送钉钉消息失败: {e}")
        return False

def main():
    # 配置群聊名称
    dingtalk_group_name = "您的钉钉群名称"
    wechat_group_name = "您的微信群名称"
    
    # 连接到应用
    dingtalk_window, wechat_window = connect_to_apps()
    if not dingtalk_window or not wechat_window:
        logger.error("无法连接到应用，程序退出")
        return
    
    logger.info("成功连接到钉钉和微信")
    
    try:
        while True:
            # 获取钉钉最新消息并转发到微信
            dingtalk_messages = get_dingtalk_messages(dingtalk_window, dingtalk_group_name)
            for message in dingtalk_messages:
                formatted_message = f"【{message['sender']}】: {message['content']}"
                send_to_wechat(wechat_window, wechat_group_name, formatted_message)
            
            # 获取微信最新消息并有条件地转发到钉钉
            wechat_messages = get_wechat_messages(wechat_window, wechat_group_name)
            for message in wechat_messages:
                # 应用筛选条件
                if should_forward_wechat_message(message['content'], message['sender']):
                    formatted_message = f"【微信-{message['sender']}】: {message['content']}"
                    send_to_dingtalk(dingtalk_window, dingtalk_group_name, formatted_message)
            
            # 等待一段时间再检查新消息
            time.sleep(5)
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()




实现难点与解决方案
1. 元素定位
难点：钉钉和微信的UI元素可能没有明确的ID或名称。

解决方案：

使用控件类型、类名和索引组合定位
使用层次结构导航（父子关系）
使用PyWinAuto的Spy++工具分析UI结构
2. 消息去重
难点：避免重复处理同一条消息。

解决方案：

使用集合记录已处理的消息ID
基于发送者和内容创建唯一标识符
3. 消息筛选
难点：根据条件筛选微信消息。

解决方案：

实现自定义筛选函数
可以基于关键词、发送者、时间等条件筛选
4. 错误处理
难点：UI自动化容易受外部因素影响。

解决方案：

全面的异常处理
详细的日志记录
重试机制
使用建议
先测试元素定位：
使用PyWinAuto的Spy++工具分析钉钉和微信的UI结构
调整代码中的元素定位方式
逐步实现功能：
先实现钉钉到微信的单向转发
再实现微信到钉钉的条件转发
最后优化和完善错误处理
定制筛选条件：
根据实际需求修改should_forward_wechat_message函数
可以添加更复杂的条件，如正则表达式匹配
考虑添加功能：
消息格式化（如添加前缀标识来源）
图片和文件处理
定时启停功能
结论
使用PyWinAuto实现钉钉群与微信群的消息同步是完全可行的，特别是在两个应用都已打开的情况下。关键是正确定位UI元素和处理各种异常情况。

上述代码框架提供了一个良好的起点，但可能需要根据您的实际环境进行调整，特别是元素定位部分。建议先小规模测试，确保基本功能正常后再扩展功能。



运行方式分析：Python脚本、EXE还是CMD
对于这个PyWinAuto自动化脚本，您有多种运行方式可以选择。我将详细分析每种方式的优缺点，帮助您选择最适合的运行方式。

1. 通过CMD/PowerShell运行Python脚本
运行方式：python dingtalk_wechat_sync.py

优点：

简单直接，无需额外步骤
便于调试，可以实时查看输出和错误信息
容易修改脚本内容进行调整
缺点：

需要保持命令行窗口打开
用户需要安装Python和相关依赖
不够用户友好，特别是对非技术用户
适用场景：

开发和测试阶段
技术用户自用
需要频繁调整脚本内容
2. 打包为EXE可执行文件
打包方法：
使用PyInstaller或Auto-py-to-exe等工具将Python脚本打包为EXE文件

# 安装PyInstaller
pip install pyinstaller

# 打包脚本
pyinstaller --onefile --noconsole dingtalk_wechat_sync.py


优点：

用户无需安装Python和依赖
双击即可运行，更加用户友好
可以隐藏控制台窗口（使用--noconsole选项）
可以添加图标和版本信息，更专业
缺点：

文件体积较大（包含Python解释器和依赖）
打包后不易修改
可能被杀毒软件误报
调试困难
适用场景：

分发给非技术用户
最终稳定版本
需要更专业的外观和体验
3. 使用Windows计划任务自动运行
设置方法：

创建批处理文件（.bat）调用Python脚本
使用Windows计划任务定时执行

批处理文件内容示例：
@echo off
cd /d "C:\path\to\script\folder"
python dingtalk_wechat_sync.py

优点：

可以设置自动启动和定时运行
可以在后台运行，不干扰用户
可以设置条件触发（如登录时、空闲时等）
缺点：

设置相对复杂
仍需安装Python和依赖
调试和监控不便
适用场景：

需要长期稳定运行
需要定时或条件触发
服务器或长时间运行的计算机
4. 使用Windows服务运行
设置方法：
使用NSSM等工具将Python脚本注册为Windows服务

# 使用NSSM注册服务
nssm install DingTalkWeChatSync "C:\path\to\python.exe" "C:\path\to\dingtalk_wechat_sync.py"

优点：

开机自动启动，无需用户干预
在后台运行，完全不可见
即使用户注销也能继续运行
可以设置自动重启和恢复策略
缺点：

设置最复杂
需要管理员权限
调试非常困难
与UI交互可能有限制
适用场景：

需要作为系统服务长期运行
服务器环境
最高级别的自动化需求
推荐方案
基于您的需求（钉钉和微信消息同步），我的建议是：

开发和测试阶段：
使用CMD/PowerShell运行Python脚本，便于调试和修改。

稳定后的日常使用：
将脚本打包为EXE可执行文件，并添加以下功能：

系统托盘图标，便于查看状态和控制
日志记录功能，便于排查问题
简单的配置界面，便于调整设置

打包为EXE的完整示例：
import pywinauto
import time
import re
import logging
import sys
import os
import threading
import PySimpleGUI as sg
import pystray
from PIL import Image, ImageDraw

# 配置日志
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "sync_log.txt")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局变量
running = True
config = {
    "dingtalk_group": "您的钉钉群名称",
    "wechat_group": "您的微信群名称",
    "check_interval": 5,  # 检查间隔（秒）
    "keywords": ["重要", "紧急", "请回复", "通知"]
}

# 消息记录，避免重复处理
processed_dingtalk_messages = set()
processed_wechat_messages = set()

# 消息筛选条件
def should_forward_wechat_message(message, sender):
    return any(keyword in message for keyword in config["keywords"])

# 主同步逻辑（与前面代码相同，略）
# ...

# 创建系统托盘图标
def create_tray_icon():
    # 创建图标图像
    image = Image.new('RGB', (64, 64), color = (0, 0, 0))
    d = ImageDraw.Draw(image)
    d.rectangle((10, 10, 54, 54), fill=(0, 120, 255))
    d.text((20, 25), "DW", fill=(255, 255, 255))
    
    # 定义菜单项
    def on_quit(icon, item):
        global running
        running = False
        icon.stop()
        
    def on_show_log(icon, item):
        os.startfile(log_file)
        
    def on_show_config(icon, item):
        show_config_window()
    
    # 创建托盘图标
    icon = pystray.Icon(
        "dingtalk_wechat_sync",
        image,
        "钉钉微信消息同步",
        menu=pystray.Menu(
            pystray.MenuItem("查看日志", on_show_log),
            pystray.MenuItem("配置", on_show_config),
            pystray.MenuItem("退出", on_quit)
        )
    )
    
    icon.run()

# 配置窗口
def show_config_window():
    layout = [
        [sg.Text("钉钉群名称"), sg.Input(config["dingtalk_group"], key="dingtalk_group")],
        [sg.Text("微信群名称"), sg.Input(config["wechat_group"], key="wechat_group")],
        [sg.Text("检查间隔(秒)"), sg.Input(str(config["check_interval"]), key="check_interval")],
        [sg.Text("关键词(逗号分隔)"), sg.Input(",".join(config["keywords"]), key="keywords")],
        [sg.Button("保存"), sg.Button("取消")]
    ]
    
    window = sg.Window("配置", layout)
    
    while True:
        event, values = window.read()
        if event == sg.WINDOW_CLOSED or event == "取消":
            break
        elif event == "保存":
            config["dingtalk_group"] = values["dingtalk_group"]
            config["wechat_group"] = values["wechat_group"]
            config["check_interval"] = int(values["check_interval"])
            config["keywords"] = [k.strip() for k in values["keywords"].split(",")]
            break
    
    window.close()

# 主函数
def main():
    # 启动托盘图标线程
    tray_thread = threading.Thread(target=create_tray_icon, daemon=True)
    tray_thread.start()
    
    logger.info("钉钉微信消息同步服务已启动")
    
    # 连接到应用
    dingtalk_window, wechat_window = connect_to_apps()
    if not dingtalk_window or not wechat_window:
        logger.error("无法连接到应用，程序退出")
        return
    
    logger.info("成功连接到钉钉和微信")
    
    try:
        while running:
            # 同步逻辑（与前面代码相同）
            # ...
            
            # 使用配置的检查间隔
            time.sleep(config["check_interval"])
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()

打包命令

# 安装必要的库
pip install pywinauto pystray pillow PySimpleGUI pyinstaller

# 打包为EXE（包含托盘图标）
pyinstaller --onefile --noconsole --icon=icon.ico dingtalk_wechat_sync.py

结论
开发阶段：使用CMD/PowerShell运行Python脚本
测试阶段：使用打包的EXE，但保留控制台输出（去掉--noconsole选项）
稳定使用：使用完整打包的EXE，带托盘图标和配置界面
长期服务：考虑注册为Windows服务或使用计划任务自动运行
无论选择哪种方式，都建议实现良好的日志记录和错误处理，以便在出现问题时能够快速定位和解决。



