import time
import logging
import threading
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class Monitor:
    """监控和错误恢复模块"""
    
    def __init__(self, config_file='config.json'):
        """初始化监控模块"""
        self.config = self._load_config(config_file)
        self.max_reconnect_attempts = self.config.get('max_reconnect_attempts', 3)
        self.reconnect_delay = self.config.get('reconnect_delay', 10)
        
        self.status = {
            'dingtalk_connected': False,
            'wechat_connected': False,
            'last_dingtalk_message': None,
            'last_wechat_message': None,
            'dingtalk_message_count': 0,
            'wechat_message_count': 0,
            'errors': [],
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 启动状态保存线程
        self.running = True
        self.status_thread = threading.Thread(target=self._status_saver)
        self.status_thread.daemon = True
        self.status_thread.start()
    
    def _load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {
                'max_reconnect_attempts': 3,
                'reconnect_delay': 10
            }
    
    def _status_saver(self):
        """定期保存状态到文件"""
        while self.running:
            try:
                self.status['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                with open('status.json', 'w', encoding='utf-8') as f:
                    json.dump(self.status, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.error(f"保存状态失败: {e}")
            
            time.sleep(60)  # 每分钟保存一次
    
    def update_status(self, key, value):
        """更新状态"""
        self.status[key] = value
        if key in ['dingtalk_message_count', 'wechat_message_count']:
            self.status[key] += 1
    
    def record_error(self, error_msg):
        """记录错误"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.status['errors'].append({
            'time': timestamp,
            'message': error_msg
        })
        # 只保留最近20条错误记录
        if len(self.status['errors']) > 20:
            self.status['errors'] = self.status['errors'][-20:]
    
    def reconnect(self, connect_func, app_name):
        """尝试重新连接应用"""
        for attempt in range(self.max_reconnect_attempts):
            logger.info(f"尝试重新连接{app_name} (尝试 {attempt+1}/{self.max_reconnect_attempts})")
            try:
                result = connect_func()
                if result:
                    logger.info(f"重新连接{app_name}成功")
                    self.update_status(f"{app_name.lower()}_connected", True)
                    return result
            except Exception as e:
                logger.error(f"重新连接{app_name}失败: {e}")
                self.record_error(f"重新连接{app_name}失败: {e}")
            
            time.sleep(self.reconnect_delay)
        
        logger.error(f"重新连接{app_name}失败，已达最大重试次数")
        self.record_error(f"重新连接{app_name}失败，已达最大重试次数")
        return None
    
    def stop(self):
        """停止监控模块"""
        self.running = False
        if self.status_thread.is_alive():
            self.status_thread.join(timeout=2)
        
        # 保存最终状态
        self.status['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        try:
            with open('status.json', 'w', encoding='utf-8') as f:
                json.dump(self.status, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存最终状态失败: {e}")
    
    def get_status_summary(self):
        """获取状态摘要"""
        now = datetime.now()
        start_time = datetime.strptime(self.status['start_time'], '%Y-%m-%d %H:%M:%S')
        running_time = now - start_time
        
        hours, remainder = divmod(running_time.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        
        return {
            'running_time': f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒",
            'dingtalk_connected': self.status['dingtalk_connected'],
            'wechat_connected': self.status['wechat_connected'],
            'dingtalk_message_count': self.status['dingtalk_message_count'],
            'wechat_message_count': self.status['wechat_message_count'],
            'error_count': len(self.status['errors']),
            'last_update': self.status['last_update']
        }
