#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安卓应用自动抓包工具
这个脚本可以帮助您自动捕获安卓设备的网络流量
使用方法：
1. 确保您的电脑和安卓设备连接到同一个WiFi网络
2. 安装必要的依赖: pip install scapy tkinter
3. 运行此脚本: python auto_android_capture.py
4. 在界面中输入您安卓设备的IP地址
5. 点击"开始抓包"按钮
"""

import os
import sys
import time
import threading
from datetime import datetime
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
    from scapy.all import sniff, wrpcap, IP
except ImportError:
    print("正在安装必要的依赖...")
    os.system("pip install scapy tkinter")
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
    from scapy.all import sniff, wrpcap, IP

class AndroidPacketCapture:
    def __init__(self, root):
        self.root = root
        self.root.title("安卓应用自动抓包工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        self.packets = []
        self.is_capturing = False
        self.capture_thread = None
        
        self.setup_ui()
    
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(settings_frame, text="安卓设备IP地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.ip_entry = ttk.Entry(settings_frame, width=20)
        self.ip_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        self.ip_entry.insert(0, "192.168.1.")  # 默认IP前缀
        
        ttk.Label(settings_frame, text="保存文件名:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.filename_entry = ttk.Entry(settings_frame, width=30)
        self.filename_entry.grid(row=1, column=1, sticky=tk.W, pady=5)
        self.filename_entry.insert(0, f"android_capture_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pcap")
        
        ttk.Label(settings_frame, text="过滤应用端口 (可选):").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.port_entry = ttk.Entry(settings_frame, width=10)
        self.port_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="开始抓包", command=self.start_capture)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止抓包", command=self.stop_capture, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.save_button = ttk.Button(control_frame, text="保存抓包结果", command=self.save_capture, state=tk.DISABLED)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_label.pack(fill=tk.X, side=tk.BOTTOM, pady=5)
        
        # 抓包结果显示
        result_frame = ttk.LabelFrame(main_frame, text="抓包结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建表格
        columns = ("序号", "时间", "源地址", "目标地址", "协议", "长度")
        self.tree = ttk.Treeview(result_frame, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=80)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 帮助信息
        help_text = "使用说明:\n1. 输入安卓设备的IP地址\n2. 点击'开始抓包'\n3. 在安卓设备上使用要分析的应用\n4. 点击'停止抓包'\n5. 点击'保存抓包结果'保存为PCAP文件"
        help_label = ttk.Label(main_frame, text=help_text, background="lightyellow", padding=10)
        help_label.pack(fill=tk.X, pady=10)
    
    def packet_callback(self, packet):
        if IP in packet:
            # 检查是否与目标IP相关
            target_ip = self.ip_entry.get()
            if packet[IP].src == target_ip or packet[IP].dst == target_ip:
                # 检查端口过滤
                port_filter = self.port_entry.get()
                if port_filter:
                    if hasattr(packet, 'sport') and str(packet.sport) == port_filter:
                        pass  # 匹配源端口
                    elif hasattr(packet, 'dport') and str(packet.dport) == port_filter:
                        pass  # 匹配目标端口
                    else:
                        return  # 不匹配端口过滤条件
                
                self.packets.append(packet)
                
                # 更新UI (在主线程中)
                packet_time = datetime.fromtimestamp(packet.time).strftime('%H:%M:%S.%f')[:-3]
                protocol = packet.name if hasattr(packet, 'name') else "Unknown"
                
                packet_info = (
                    str(len(self.packets)),
                    packet_time,
                    packet[IP].src,
                    packet[IP].dst,
                    protocol,
                    str(len(packet))
                )
                
                self.root.after(0, lambda: self.tree.insert("", "end", values=packet_info))
                self.root.after(0, lambda: self.status_var.set(f"已捕获 {len(self.packets)} 个数据包"))
    
    def start_capture(self):
        if not self.ip_entry.get():
            messagebox.showerror("错误", "请输入安卓设备的IP地址")
            return
        
        self.packets = []
        self.is_capturing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.save_button.config(state=tk.DISABLED)
        self.status_var.set("正在抓包...")
        
        # 清空表格
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 在新线程中开始抓包
        self.capture_thread = threading.Thread(target=self.capture_packets)
        self.capture_thread.daemon = True
        self.capture_thread.start()
    
    def capture_packets(self):
        try:
            # 开始抓包
            sniff(prn=self.packet_callback, store=False, stop_filter=lambda x: not self.is_capturing)
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("抓包错误", str(e)))
            self.root.after(0, self.stop_capture)
    
    def stop_capture(self):
        self.is_capturing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.save_button.config(state=tk.NORMAL)
        self.status_var.set(f"抓包已停止，共捕获 {len(self.packets)} 个数据包")
    
    def save_capture(self):
        if not self.packets:
            messagebox.showinfo("提示", "没有可保存的数据包")
            return
        
        default_filename = self.filename_entry.get()
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pcap",
            filetypes=[("PCAP files", "*.pcap"), ("All files", "*.*")],
            initialfile=default_filename
        )
        
        if file_path:
            try:
                wrpcap(file_path, self.packets)
                messagebox.showinfo("成功", f"已保存 {len(self.packets)} 个数据包到 {file_path}")
                
                # 询问是否打开保存的文件
                if messagebox.askyesno("打开文件", "是否使用Wireshark打开保存的文件？\n(需要已安装Wireshark)"):
                    try:
                        os.system(f'start wireshark "{file_path}"')
                    except Exception as e:
                        messagebox.showerror("错误", f"无法打开Wireshark: {str(e)}")
            except Exception as e:
                messagebox.showerror("保存错误", str(e))

if __name__ == "__main__":
    root = tk.Tk()
    app = AndroidPacketCapture(root)
    root.mainloop()
