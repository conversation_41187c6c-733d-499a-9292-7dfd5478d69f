"ui"; // 声明使用UI模式，启用图形界面支持

importClass(java.io.File); // 导入Java的File类，用于文件操作和目录遍历


// 全局变量定义
var selectedFile = null; // 存储用户选择的单号文件路径
var mainActivity = null; // 保存主界面引用，用于界面切换和恢复
var running = false; // 脚本运行状态标志，控制脚本是否在执行中
var currentProgress = 0; // 当前处理进度，用于断点续传功能
var hasBeenPaused = false; // 是否暂停过的标志，用于决定是否显示断点续传选项

// 预设添加按钮ID
var UI_ELEMENTS = {
  // 韵达集包元素
  JIBAO: {
    ADD_BUTTON: "took_shipment_collection_add" // 集包添加按钮ID
  },
  // 韵达到派元素
  DAOPAI: {
    ADD_BUTTON: "shipment_business_shipment_give_shipment_add" // 到派添加按钮ID
  }
};

// 性能监控相关变量
var performanceStats = {
  startTime: 0,          // 脚本开始时间
  processedCount: 0,     // 处理的单号数量
  lastCheckTime: 0,      // 上次检查时间
  lastProcessedCount: 0, // 上次检查时处理的单号数量
  totalSpeed: 0,         // 总体处理速度（单号/秒）
  currentSpeed: 0        // 当前处理速度（单号/秒）
};

// 内存管理相关变量
var memoryManager = {
  lastCleanTime: 0,      // 上次清理内存的时间
  cleanInterval: 30000,  // 清理内存的时间间隔（30秒）
  performanceInterval: 30000 // 性能显示的时间间隔（30秒）
};

// 清理缓存和临时文件，防止文件累积导致脚本运行变慢
function cleanupFiles() {
  // 清理旧的记录文本文件，该文件用于记录处理进度
  var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件的存储路径
  if (files.exists(jl_path)) { // 如果文件存在
    // 直接删除记录文件，确保每次从头开始
    files.remove(jl_path); // 删除文件
  }

  // 清理Auto.js的日志和临时文件，这些文件会占用存储空间
  var appDir = "/sdcard/Android/data/org.autojs.autoxjs.v6/files"; // Auto.js的数据目录
  if (files.exists(appDir)) { // 如果目录存在
    var deletedCount = 0; // 记录删除的文件数量

    // 安全清理函数 - 只清理日志和临时文件，避免删除重要文件
    function safeCleanDirectory(dir) {
      if (!files.exists(dir)) return 0; // 如果目录不存在，返回0

      var fileList = files.listDir(dir); // 获取目录中的所有文件和文件夹
      var count = 0; // 记录删除的文件数量

      // 定义安全的文件扩展名列表（可以删除的文件类型）
      var safeExtensions = [".log", ".tmp", ".temp", ".cache"]; // 只删除这些类型的文件

      // 遍历处理每个文件和文件夹
      for (var i = 0; i < fileList.length; i++) {
        var fileName = fileList[i]; // 当前文件名
        var path = dir + "/" + fileName; // 完整路径

        if (files.isDir(path)) { // 如果是目录
          // 如果是logs目录，可以完全清理，因为日志文件可以安全删除
          if (fileName === "logs") {
            try {
              // 清理logs目录中的所有文件
              var logFiles = files.listDir(path); // 获取日志目录中的所有文件
              for (var j = 0; j < logFiles.length; j++) {
                try {
                  files.remove(path + "/" + logFiles[j]); // 删除日志文件
                  count++; // 增加计数器
                } catch (e) {
                  // 忽略删除失败的错误，继续处理下一个文件
                }
              }
            } catch (e) {
              // 忽略读取目录失败的错误
            }
          } else {
            // 对其他目录，递归应用相同的安全清理逻辑
            count += safeCleanDirectory(path); // 递归清理子目录，并累加删除数量
          }
        } else { // 如果是文件
          // 检查文件是否是安全可删除的类型
          var isSafeToDelete = false; // 默认不安全

          // 检查文件扩展名是否在安全列表中
          for (var ext of safeExtensions) {
            if (fileName.endsWith(ext)) { // 如果文件名以安全扩展名结尾
              isSafeToDelete = true; // 标记为安全可删除
              break; // 找到匹配就跳出循环
            }
          }

          // 如果是安全的文件类型，删除它
          if (isSafeToDelete) {
            try {
              files.remove(path); // 删除文件
              count++; // 增加计数器
            } catch (e) {
              // 忽略删除文件失败的错误，继续处理
            }
          }
        }
      }

      return count; // 返回删除的文件数量
    }

    // 执行安全清理并获取删除的文件数量
    deletedCount = safeCleanDirectory(appDir);

    // 返回清理的文件数量，用于显示清理结果
    return deletedCount;
  }

  // 清理控制台缓存，使控制台显示更清晰
  console.clear();
}

// 不在脚本启动时清理文件，而是在退出脚本时清理，避免不必要的清理操作

/**
 * 清理内存并释放资源
 * 调用系统垃圾回收并释放不再使用的对象
 */
function cleanMemory() {
  // 手动触发垃圾回收
  // runtime.gc() 在Auto.js中不存在，使用其他方式清理内存
  // 使用JavaScript的内存清理方法
  var largeArray = new Array(10000); // 创建大数组
  largeArray = null; // 释放引用

  // 强制运行垃圾回收
  if (global.gc) {
    global.gc();
  } else {
    // 如果不支持直接调用gc，尝试其他方法
    for (var i = 0; i < 10; i++) {
      var uselessArray = new Array(1000000);
      uselessArray = null;
    }
  }

  // 记录清理时间
  memoryManager.lastCleanTime = Date.now();

  // 不输出内存清理信息，避免干扰用户查看重要信息
  // console.info("内存清理完成");
}

/**
 * 更新并显示性能统计信息
 * @param {number} currentCount - 当前处理的单号数量
 */
function updatePerformanceStats(currentCount) {
  var now = Date.now();

  // 如果是第一次调用，初始化开始时间
  if (performanceStats.startTime === 0) {
    performanceStats.startTime = now;
    performanceStats.lastCheckTime = now;
    performanceStats.lastProcessedCount = currentCount;
    return; // 第一次调用仅初始化，不显示统计
  }

  // 计算处理速度
  var countDiff = currentCount - performanceStats.lastProcessedCount;
  var intervalTimeSec = (now - performanceStats.lastCheckTime) / 1000;

  // 避免除以0
  if (intervalTimeSec > 0) {
    performanceStats.currentSpeed = (countDiff / intervalTimeSec).toFixed(2);
  }

  // 输出性能信息
  console.info("当前性能: " + performanceStats.currentSpeed + " 单号/秒");

  // 更新上次检查时间和处理数量
  performanceStats.lastCheckTime = now;
  performanceStats.lastProcessedCount = currentCount;
}

// 初始化界面元素，创建主界面UI
ui.layout(
  <vertical bg="#ffffff" padding="16" marginTop="5">
    <text text="自动化工具启动界面" textSize="24sp" textColor="#000000" gravity="center" typeface="serif"/>
    <button id="selectFileBtn" text="请选择单号文件" marginTop="16" style="Widget.AppCompat.Button.Colored" textSize="18sp" typeface="serif"/>
    <list id="fileList" h="320" marginTop="8">
      <horizontal>
        <text id="fileName" text="{{fileName}}" textColor="#000000" textSize="18sp" padding="8" typeface="serif"/>
      </horizontal>
    </list>
    <text
      id="selectedFileText"
      text="已选择单号文件："
      textColor="#ffffff"
      bg="#4CAF50"
      padding="8"
      marginTop="12"
      textSize="18sp"
      typeface="serif"
      lines="2"
      ellipsize="end"
      w="*"
      h="auto"
      gravity="left"
    />
    <horizontal marginTop="24" gravity="center">
      <button id="btnJibao" text="韵达集包" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnDaopai" text="韵达到派" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" marginRight="6" textSize="18sp" typeface="serif"/>
      <button id="btnXiangzhen" text="乡镇优化" bg="#FFEB3B" textColor="#000000" w="0" layout_weight="1" textSize="18sp" typeface="serif"/>
    </horizontal>
  </vertical>
);

// 加载 txt 文件列表，扫描指定目录下的所有txt文件
function loadTxtFiles() {
  let dir = new File("/sdcard/Pictures"); // 创建File对象指向图片目录
  if (!dir.exists() || !dir.isDirectory()) { // 检查目录是否存在且是否是目录
    toast("目标文件夹不存在！"); // 显示错误提示
    return []; // 返回空数组
  }

  let files = dir.listFiles(); // 获取目录中的所有文件
  let txtFiles = []; // 初始化空数组存储txt文件

  if (files) { // 如果成功获取到文件列表
    for (let f of files) { // 遍历每个文件
      if (f.isFile() && f.getName().endsWith(".txt")) { // 只选择txt文件
        txtFiles.push({ // 将文件信息添加到数组
          fileName: f.getName(), // 文件名
          fullPath: f.getAbsolutePath() // 文件完整路径
        });
      }
    }
  }
  return txtFiles; // 返回所有找到的txt文件列表
}

// 文件选择按钮点击逻辑
ui.selectFileBtn.on("click", () => { // 注册点击事件处理函数
  let fileList = loadTxtFiles(); // 加载txt文件列表
  if (fileList.length === 0) { // 如果没有找到txt文件
    toast("未找到 txt 文件"); // 显示提示
  } else { // 如果找到了txt文件
    ui.fileList.setDataSource(fileList); // 将文件列表设置为列表控件的数据源
  }
});

// 文件列表项点击事件
ui.fileList.on("item_click", (item) => { // 注册列表项点击事件
  selectedFile = item.fullPath; // 将选中的文件路径保存到全局变量
  // 始终保持两行显示，第一行是标题，第二行是文件名
  ui.selectedFileText.setText("已选择单号文件：\n" + item.fileName); // 更新选中文件显示
  toast("已选择: " + item.fileName); // 显示选中文件的提示
});

// 韵达集包按钮点击事件
ui.btnJibao.on("click", () => { // 注册点击事件处理函数
  if (!selectedFile) return toast("请先选择单号文件！"); // 检查是否选择了文件

  // 不再删除记录文件，以便断点续传
  // 记录文件的删除操作已移动到退出脚本按钮点击事件中

  // 显示功能按钮提示，告知用户已启动
  toast("启动韵达集包"); // 显示启动提示

  // 保存主界面引用，用于后续返回
  mainActivity = activity; // 保存当前活动引用

  // 先创建悬浮窗，再隐藏主界面，确保界面切换顺序正确
  setTimeout(function() { // 设置延时执行
    launchFloatingWindow(selectedFile); // 启动悬浮窗并传入选中的文件路径
    // 使用setVisibility而非ui.finish()来隐藏主界面，避免完全结束活动
    ui.setContentView(ui.inflate(<frame/>)); // 将当前内容视图设置为空白帧布局
  }, 500); // 延时500毫秒
});

// 韵达到派按钮点击事件
ui.btnDaopai.on("click", () => { // 注册点击事件处理函数
  if (!selectedFile) return toast("请先选择单号文件！"); // 检查是否选择了文件

  // 不再删除记录文件，以便断点续传
  // 记录文件的删除操作已移动到退出脚本按钮点击事件中

  // 显示功能按钮提示
  toast("启动韵达到派"); // 显示启动提示

  // 保存主界面引用，用于后续返回
  mainActivity = activity; // 保存当前活动引用

  // 先创建悬浮窗，再隐藏主界面，确保界面切换顺序正确
  setTimeout(function() { // 设置延时执行
    launchFloatingWindowDaopai(selectedFile); // 启动到派悬浮窗并传入选中的文件路径
    // 使用setVisibility而非ui.finish()来隐藏主界面，避免完全结束活动
    ui.setContentView(ui.inflate(<frame/>)); // 将当前内容视图设置为空白帧布局
  }, 500); // 延时500毫秒
});

// 乡镇优化按钮点击事件（占位功能，实际未实现具体功能）
ui.btnXiangzhen.on("click", () => { // 注册点击事件处理函数
  if (!selectedFile) return toast("请先选择单号文件！"); // 检查是否选择了文件

  // 不再删除记录文件，以便断点续传
  // 记录文件的删除操作已移动到退出脚本按钮点击事件中

  // 显示功能按钮提示
  toast("启动乡镇优化"); // 显示启动提示

  // 保存主界面引用，用于后续返回
  mainActivity = activity; // 保存当前活动引用

  // 使用相同的方式隐藏主界面
  setTimeout(function() { // 设置延时执行
    ui.setContentView(ui.inflate(<frame/>)); // 将当前内容视图设置为空白帧布局
  }, 500); // 延时500毫秒
});

// ============ 创建悬浮窗逻辑 ============
// 创建悬浮窗并设置相关事件处理

/**
 * 启动悬浮窗并初始化相关功能
 * @param {string} filePath - 选中的单号文件路径
 */
function launchFloatingWindow(filePath) {
  // 创建包含三个按钮的悬浮窗：启动、暂停和关闭
  var window = floaty.window(
    <horizontal>
      <button id="start" text="启动" w="80" h="50" />
      <button id="pause" text="暂停" w="80" h="50" />
      <button id="closeBtn" text="关闭" w="80" h="50" />
    </horizontal>
  );

  // 设置悬浮窗的初始位置，距离屏幕左上角10像素
  window.setPosition(10, 10);

  // 保持 UI 活动，防止悬浮窗被系统回收
  setInterval(() => {}, 1000); // 每秒执行一次空操作，保持活动

  // 初始化状态变量
  running = false; // 设置脚本初始状态为非运行状态
  var scriptThread = null; // 脚本线程引用，用于控制脚本执行

  // 重置进度相关变量
  currentProgress = 0; // 当前处理进度重置为0
  hasBeenPaused = false; // 重置暂停标志

  // 存储当前显示的选择弹窗引用
  var currentStartOptionsWindow = null;

  // 启动按钮点击事件处理
  window.start.click(() => {
    if (!running) { // 如果脚本当前不在运行
      // 如果选择弹窗已经显示，则关闭它
      if (currentStartOptionsWindow) {
        currentStartOptionsWindow.close();
        currentStartOptionsWindow = null;
        return; // 退出函数，不再创建新的弹窗
      }

      // 始终显示选择弹窗，确保用户体验一致
      // 使用悬浮窗而非对话框来避免 BadTokenException错误
      let startOptionsWindow = floaty.window(
        <vertical bg="#00000000" padding="0" w="100">
          <button id="fromStartBtn" text="从头开始" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
          <button id="continueBtn" text="断点继续" bg="#4CAF50" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        </vertical>
      );

      // 保存当前弹窗引用
      currentStartOptionsWindow = startOptionsWindow;

      // 设置选择弹窗的位置，与关闭按钮的弹窗位置一致
      startOptionsWindow.setPosition(17, 105); // 固定位置

      // 从头开始按钮点击事件
      startOptionsWindow.fromStartBtn.click(() => {
        // 关闭弹窗并设置状态
        startOptionsWindow.close(); // 关闭选择弹窗
        currentStartOptionsWindow = null; // 清除弹窗引用
        running = true; // 设置脚本为运行状态
        toast("从头开始运行脚本"); // 显示提示

        // 重置进度为0，从头开始
        currentProgress = 0; // 重置当前进度

        // 创建新线程执行脚本，避免阻塞UI线程
        scriptThread = threads.start(function () {
          console.show(); // 显示控制台
          console.setPosition(5, 825); // 设置控制台位置

          // 设置控制台标题为文件名(不含.txt后缀)
          var fileName = filePath.split("/").pop().replace(".txt", ""); // 提取文件名并移除.txt后缀
          console.setTitle(fileName); // 设置控制台标题

          runJibaoScript(filePath, 0); // 从头开始执行集包脚本
        });
      });

      // 断点继续按钮点击事件
      startOptionsWindow.continueBtn.click(() => {
        // 关闭弹窗并设置状态
        startOptionsWindow.close(); // 关闭选择弹窗
        currentStartOptionsWindow = null; // 清除弹窗引用
        running = true; // 设置脚本为运行状态
        toast("断点继续运行脚本"); // 显示提示

        // 从记录文件读取上次的进度
        var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
        var lastProgress = 0; // 默认从0开始

        if (files.exists(jl_path)) {
          try {
            // 读取记录文件中的最后一行作为上次的进度
            var content = files.read(jl_path);
            var lines = content.split("\n").filter(line => line.trim() !== "");
            if (lines.length > 0) {
              lastProgress = parseInt(lines[lines.length - 1]);
              if (isNaN(lastProgress)) {
                lastProgress = 0; // 如果解析失败，从0开始
              }
            }
            console.log("从记录文件读取到上次进度: " + lastProgress);
          } catch (e) {
            console.error("读取进度失败: " + e);
          }
        } else {
          console.log("记录文件不存在，从头开始");
        }

        // 更新全局进度变量
        currentProgress = lastProgress;

        // 创建新线程执行脚本，避免阻塞UI线程
        scriptThread = threads.start(function () {
          console.show(); // 显示控制台
          console.setPosition(5, 825); // 设置控制台位置

          // 设置控制台标题为文件名(不含.txt后缀)
          var fileName = filePath.split("/").pop().replace(".txt", ""); // 提取文件名并移除.txt后缀
          console.setTitle(fileName); // 设置控制台标题

          runJibaoScript(filePath, currentProgress); // 从断点位置继续执行集包脚本
        });
      });
    }
  });

  // 暂停按钮点击事件处理
  window.pause.click(() => {
    if (running) { // 如果脚本当前正在运行
      running = false; // 设置脚本为非运行状态
      hasBeenPaused = true; // 标记已经暂停过，用于后续显示选择弹窗
      toast("脚本已暂停"); // 显示暂停提示

      // 在暂停时立即保存当前进度到记录文件
      var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
      try {
        // 清空记录文件并写入当前进度
        files.write(jl_path, currentProgress + "\n");
        console.log("已保存当前进度: " + currentProgress);
      } catch (e) {
        console.error("保存进度失败: " + e);
      }

      // 强制停止所有脚本线程，确保脚本完全停止
      if (scriptThread) { // 如果脚本线程存在
        scriptThread.interrupt(); // 先尝试中断主线程
      }

      // 使用threads.shutDownAll()强制停止所有线程，确保完全停止
      // 这可能会导致日志输出不完整，但能确保脚本完全停止
      threads.shutDownAll();

      // 创建新线程来处理暂停后的操作，避免主线程被关闭
      setTimeout(function() {
        // 重新初始化脚本状态
        toast("脚本已完全停止");
      }, 500);
    }
  });

  // 存储当前显示的关闭选项弹窗引用
  var currentCloseOptionsWindow = null;

  // 关闭按钮点击事件处理
  window.closeBtn.click(() => {
    // 如果选择弹窗已经显示，则关闭它
    if (currentCloseOptionsWindow) {
      currentCloseOptionsWindow.close();
      currentCloseOptionsWindow = null;
      return; // 退出函数，不再创建新的弹窗
    }

    // 使用悬浮窗而非对话框来避免 BadTokenException错误
    let optionsWindow = floaty.window(
      <vertical bg="#00000000" padding="0" w="100">
        <button id="returnBtn" text="返回选择" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        <button id="exitBtn" text="退出脚本" bg="#F44336" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
      </vertical>
    );

    // 保存当前弹窗引用
    currentCloseOptionsWindow = optionsWindow;

    // 设置选择弹窗的位置
    // 直接使用固定值作为X和Y坐标，确保在屏幕上的位置正确
    optionsWindow.setPosition(17, 105); // 固定位置

    // 返回选择按钮点击事件，返回到主界面
    optionsWindow.returnBtn.click(() => {
      // 先显示提示，避免用户疑惑操作是否生效
      toast("返回主界面"); // 显示返回提示

      // 关闭所有悬浮窗和控制台
      optionsWindow.close(); // 关闭选项悬浮窗
      currentCloseOptionsWindow = null; // 清除弹窗引用
      window.close(); // 关闭主悬浮窗
      console.hide(); // 隐藏控制台，避免返回主界面后仍显示控制台

      // 重新启动主界面 - 简化版
      // 清除所有全局变量状态
      running = false; // 重置运行状态
      currentProgress = 0; // 重置进度
      hasBeenPaused = false; // 重置暂停标志
      threads.shutDownAll(); // 关闭所有子线程

      // 使用延时确保悬浮窗已关闭后再重启主界面
      setTimeout(function() {
        // 检测是否是打包环境 - 简化版
        var isPackaged = false; // 默认不是打包环境

        // 直接检查应用包名是否为"com.yxd"
        try {
          var packageName = context.getPackageName();
          // 如果包名是"com.yxd"，则是打包环境
          if (packageName === "com.yxd") {
            isPackaged = true;
          }
        } catch(e) {
          // 如果获取包名出错，默认不是打包环境
        }

        // 获取当前脚本路径，用于调试模式下重启脚本
        var currentSource = engines.myEngine().getSource();

        // 显示统一提示
        toast("返回主界面");

        if (isPackaged) { // 如果是打包环境（运行模式）
          // 运行模式下重启应用
          var packageName = context.getPackageName(); // 获取当前应用包名
          app.launch(packageName); // 启动应用

          // 给应用一些时间启动，然后退出当前脚本
          setTimeout(function() {
            exit(); // 退出当前脚本
          }, 1000); // 延时1秒
        } else { // 如果是调试模式（非打包环境）
          // 调试模式下直接返回主界面
          if (currentSource) {
            // 执行脚本文件
            engines.execScriptFile(currentSource);
            // 等待新脚本启动后停止当前脚本
            setTimeout(function() {
              engines.myEngine().forceStop();
            }, 500);
          } else {
            toast("返回主界面失败，请手动重启");
            exit();
          }
        }
      }, 300); // 延时300毫秒
    });

    // 退出脚本按钮点击事件
    optionsWindow.exitBtn.click(() => {
      toast("脚本已退出"); // 显示退出提示

      // 在退出前清理文件
      var cleanedCount = cleanupFiles(); // 调用清理函数并获取清理的文件数量
      if (cleanedCount > 0) { // 如果有文件被清理
        console.log("已清理" + cleanedCount + "个缓存文件"); // 在控制台输出清理信息
      }

      // 在退出脚本时删除记录文件，而不是在启动时删除
      var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
      if (files.exists(jl_path)) { // 如果文件存在
        files.remove(jl_path); // 删除文件
        console.log("已删除记录文件"); // 在控制台输出删除信息
      }

      // 关闭所有悬浮窗和控制台
      optionsWindow.close(); // 关闭选项悬浮窗
      currentCloseOptionsWindow = null; // 清除弹窗引用
      window.close(); // 关闭主悬浮窗
      console.hide(); // 隐藏控制台

      // 关闭所有线程并退出脚本
      threads.shutDownAll(); // 关闭所有子线程
      exit(); // 退出脚本
    });
  });
}

/**
 * 启动韵达到派悬浮窗并初始化相关功能
 * @param {string} filePath - 选中的单号文件路径
 */
function launchFloatingWindowDaopai(filePath) {
  // 创建包含三个按钮的悬浮窗：启动、暂停和关闭
  var window = floaty.window(
    <horizontal>
      <button id="start" text="启动" w="80" h="50" />
      <button id="pause" text="暂停" w="80" h="50" />
      <button id="closeBtn" text="关闭" w="80" h="50" />
    </horizontal>
  );

  // 设置悬浮窗的初始位置，距离屏幕左上角10像素
  window.setPosition(10, 10);

  // 保持 UI 活动，防止悬浮窗被系统回收
  setInterval(() => {}, 1000); // 每秒执行一次空操作，保持活动

  // 初始化状态变量
  running = false; // 设置脚本初始状态为非运行状态
  var scriptThread = null; // 脚本线程引用，用于控制脚本执行

  // 重置进度相关变量
  currentProgress = 0; // 当前处理进度重置为0
  hasBeenPaused = false; // 重置暂停标志

  // 存储当前显示的选择弹窗引用
  var currentStartOptionsWindow = null;

  // 启动按钮点击事件处理
  window.start.click(() => {
    if (!running) { // 如果脚本当前不在运行
      // 如果选择弹窗已经显示，则关闭它
      if (currentStartOptionsWindow) {
        currentStartOptionsWindow.close();
        currentStartOptionsWindow = null;
        return; // 退出函数，不再创建新的弹窗
      }

      // 始终显示选择弹窗，确保用户体验一致
      // 使用悬浮窗而非对话框来避免 BadTokenException错误
      let startOptionsWindow = floaty.window(
        <vertical bg="#00000000" padding="0" w="100">
          <button id="fromStartBtn" text="从头开始" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
          <button id="continueBtn" text="断点继续" bg="#4CAF50" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        </vertical>
      );

      // 保存当前弹窗引用
      currentStartOptionsWindow = startOptionsWindow;

      // 设置选择弹窗的位置，与关闭按钮的弹窗位置一致
      startOptionsWindow.setPosition(17, 105); // 固定位置

      // 从头开始按钮点击事件
      startOptionsWindow.fromStartBtn.click(() => {
        // 关闭弹窗并设置状态
        startOptionsWindow.close(); // 关闭选择弹窗
        currentStartOptionsWindow = null; // 清除弹窗引用
        running = true; // 设置脚本为运行状态
        toast("从头开始运行脚本"); // 显示提示

        // 重置进度为0，从头开始
        currentProgress = 0; // 重置当前进度

        // 创建新线程执行脚本，避免阻塞UI线程
        scriptThread = threads.start(function () {
          console.show(); // 显示控制台
          console.setPosition(5, 825); // 设置控制台位置

          // 设置控制台标题为文件名(不含.txt后缀)
          var fileName = filePath.split("/").pop().replace(".txt", ""); // 提取文件名并移除.txt后缀
          console.setTitle(fileName); // 设置控制台标题

          runDaopaiScript(filePath, 0); // 从头开始执行到派脚本
        });
      });

      // 断点继续按钮点击事件
      startOptionsWindow.continueBtn.click(() => {
        // 关闭弹窗并设置状态
        startOptionsWindow.close(); // 关闭选择弹窗
        currentStartOptionsWindow = null; // 清除弹窗引用
        running = true; // 设置脚本为运行状态
        toast("断点继续运行脚本"); // 显示提示

        // 从记录文件读取上次的进度
        var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
        var lastProgress = 0; // 默认从0开始

        if (files.exists(jl_path)) {
          try {
            // 读取记录文件中的最后一行作为上次的进度
            var content = files.read(jl_path);
            var lines = content.split("\n").filter(line => line.trim() !== "");
            if (lines.length > 0) {
              lastProgress = parseInt(lines[lines.length - 1]);
              if (isNaN(lastProgress)) {
                lastProgress = 0; // 如果解析失败，从0开始
              }
            }
            console.log("从记录文件读取到上次进度: " + lastProgress);
          } catch (e) {
            console.error("读取进度失败: " + e);
          }
        } else {
          console.log("记录文件不存在，从头开始");
        }

        // 更新全局进度变量
        currentProgress = lastProgress;

        // 创建新线程执行脚本，避免阻塞UI线程
        scriptThread = threads.start(function () {
          console.show(); // 显示控制台
          console.setPosition(5, 825); // 设置控制台位置

          // 设置控制台标题为文件名(不含.txt后缀)
          var fileName = filePath.split("/").pop().replace(".txt", ""); // 提取文件名并移除.txt后缀
          console.setTitle(fileName); // 设置控制台标题

          runDaopaiScript(filePath, currentProgress); // 从断点位置继续执行到派脚本
        });
      });
    }
  });

  // 暂停按钮点击事件处理
  window.pause.click(() => {
    if (running) { // 如果脚本当前正在运行
      running = false; // 设置脚本为非运行状态
      hasBeenPaused = true; // 标记已经暂停过，用于后续显示选择弹窗
      toast("脚本已暂停"); // 显示暂停提示

      // 在暂停时立即保存当前进度到记录文件
      var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
      try {
        // 清空记录文件并写入当前进度
        files.write(jl_path, currentProgress + "\n");
        console.log("已保存当前进度: " + currentProgress);
      } catch (e) {
        console.error("保存进度失败: " + e);
      }

      // 强制停止所有脚本线程，确保脚本完全停止
      if (scriptThread) { // 如果脚本线程存在
        scriptThread.interrupt(); // 先尝试中断主线程
      }

      // 使用threads.shutDownAll()强制停止所有线程，确保完全停止
      // 这可能会导致日志输出不完整，但能确保脚本完全停止
      threads.shutDownAll();

      // 创建新线程来处理暂停后的操作，避免主线程被关闭
      setTimeout(function() {
        // 重新初始化脚本状态
        toast("脚本已完全停止");
      }, 500);
    }
  });

  // 存储当前显示的关闭选项弹窗引用
  var currentCloseOptionsWindow = null;

  // 关闭按钮点击事件处理
  window.closeBtn.click(() => {
    // 如果选择弹窗已经显示，则关闭它
    if (currentCloseOptionsWindow) {
      currentCloseOptionsWindow.close();
      currentCloseOptionsWindow = null;
      return; // 退出函数，不再创建新的弹窗
    }

    // 使用悬浮窗而非对话框来避免 BadTokenException错误
    let optionsWindow = floaty.window(
      <vertical bg="#00000000" padding="0" w="100">
        <button id="returnBtn" text="返回选择" bg="#2196F3" textColor="#FFFFFF" w="*" h="40" marginBottom="4" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
        <button id="exitBtn" text="退出脚本" bg="#F44336" textColor="#FFFFFF" w="*" h="40" textSize="14sp" typeface="serif" style="Widget.AppCompat.Button.Small"/>
      </vertical>
    );

    // 保存当前弹窗引用
    currentCloseOptionsWindow = optionsWindow;

    // 设置选择弹窗的位置
    // 直接使用固定值作为X和Y坐标，确保在屏幕上的位置正确
    optionsWindow.setPosition(17, 105); // 固定位置

    // 返回选择按钮点击事件，返回到主界面
    optionsWindow.returnBtn.click(() => {
      // 先显示提示，避免用户疑惑操作是否生效
      toast("返回主界面"); // 显示返回提示

      // 关闭所有悬浮窗和控制台
      optionsWindow.close(); // 关闭选项悬浮窗
      currentCloseOptionsWindow = null; // 清除弹窗引用
      window.close(); // 关闭主悬浮窗
      console.hide(); // 隐藏控制台，避免返回主界面后仍显示控制台

      // 重新启动主界面 - 简化版
      // 清除所有全局变量状态
      running = false; // 重置运行状态
      currentProgress = 0; // 重置进度
      hasBeenPaused = false; // 重置暂停标志
      threads.shutDownAll(); // 关闭所有子线程

      // 使用延时确保悬浮窗已关闭后再重启主界面
      setTimeout(function() {
        // 检测是否是打包环境 - 简化版
        var isPackaged = false; // 默认不是打包环境

        // 直接检查应用包名是否为"com.yxd"
        try {
          var packageName = context.getPackageName();
          // 如果包名是"com.yxd"，则是打包环境
          if (packageName === "com.yxd") {
            isPackaged = true;
          }
        } catch(e) {
          // 如果获取包名出错，默认不是打包环境
        }

        // 获取当前脚本路径，用于调试模式下重启脚本
        var currentSource = engines.myEngine().getSource();

        // 显示统一提示
        toast("返回主界面");

        if (isPackaged) { // 如果是打包环境（运行模式）
          // 运行模式下重启应用
          var packageName = context.getPackageName(); // 获取当前应用包名
          app.launch(packageName); // 启动应用

          // 给应用一些时间启动，然后退出当前脚本
          setTimeout(function() {
            exit(); // 退出当前脚本
          }, 1000); // 延时1秒
        } else { // 如果是调试模式（非打包环境）
          // 调试模式下直接返回主界面
          if (currentSource) {
            // 执行脚本文件
            engines.execScriptFile(currentSource);
            // 等待新脚本启动后停止当前脚本
            setTimeout(function() {
              engines.myEngine().forceStop();
            }, 500);
          } else {
            toast("返回主界面失败，请手动重启");
            exit();
          }
        }
      }, 300); // 延时300毫秒
    });

    // 退出脚本按钮点击事件
    optionsWindow.exitBtn.click(() => {
      toast("脚本已退出"); // 显示退出提示

      // 在退出前清理文件
      var cleanedCount = cleanupFiles(); // 调用清理函数并获取清理的文件数量
      if (cleanedCount > 0) { // 如果有文件被清理
        console.log("已清理" + cleanedCount + "个缓存文件"); // 在控制台输出清理信息
      }

      // 在退出脚本时删除记录文件，而不是在启动时删除
      var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
      if (files.exists(jl_path)) { // 如果文件存在
        files.remove(jl_path); // 删除文件
        console.log("已删除记录文件"); // 在控制台输出删除信息
      }

      // 关闭所有悬浮窗和控制台
      optionsWindow.close(); // 关闭选项悬浮窗
      currentCloseOptionsWindow = null; // 清除弹窗引用
      window.close(); // 关闭主悬浮窗
      console.hide(); // 隐藏控制台

      // 关闭所有线程并退出脚本
      threads.shutDownAll(); // 关闭所有子线程
      exit(); // 退出脚本
    });
  });
}

// ============ 主脚本逻辑 ============
// 韵达集包的核心处理逻辑

/**
 * 执行韵达集包脚本，处理单号文件
 * @param {string} filePath - 单号文件路径
 * @param {number} startIndex - 开始处理的索引位置，用于断点续传
 */
function runJibaoScript(filePath, startIndex) {
  // 记录文件路径，用于保存处理进度
  var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
  var jl_hs = 0; // 初始化记录行数为0

  // 创建或打开记录文件
  if (files.create(jl_path)) { // 如果文件不存在，创建新文件
    sleep(150); // 等待文件创建完成
  } else { // 如果文件已存在
    try {
      // 读取记录文件的所有行
      var arr_jl = open(jl_path).readlines(); // 读取文件内容
      // 获取最后一行的值作为起始位置，如果文件为空则从0开始
      jl_hs = arr_jl.length === 0 ? 0 : parseInt(arr_jl[arr_jl.length - 1]) + 1;
    } catch (e) {
      // 如果读取文件出错，重新创建文件
      files.remove(jl_path); // 删除可能损坏的文件
      files.create(jl_path); // 创建新文件
      jl_hs = 0; // 重置起始位置为0
    }
  }

  // 确定开始处理的位置：优先使用传入的起始索引，如果没有则使用记录文件中的进度
  var startPos = (startIndex !== undefined && startIndex > 0) ? startIndex : jl_hs;

  // 读取单号文件内容
  var arr_a = open(filePath).readlines(); // 读取单号文件的所有行
  console.warn("当前单号文本数量: " + arr_a.length); // 输出单号总数

  // 根据是否是断点继续显示不同的信息
  if (startIndex !== undefined && startIndex > 0) {
    console.warn("从已保存进度: " + startIndex + " 条继续处理"); // 显示从已保存进度继续
  } else {
    console.warn("从第 " + (startPos + 1) + " 条开始处理"); // 显示从头开始或从记录文件读取的进度开始
  }

  // 初始化性能统计
  var now = Date.now(); // 只获取一次当前时间，提高效率
  performanceStats.startTime = now;
  performanceStats.lastCheckTime = now;
  performanceStats.lastProcessedCount = startPos;
  performanceStats.processedCount = startPos;

  // 初始化内存管理
  memoryManager.lastCleanTime = now;

// 不再预先查找并缓存UI元素，而是在每次循环中重新查找
// 移除了处理单个单号的函数，直接在循环中处理

// 不需要额外的currentPos变量，因为已经有i作为循环索引

// 不再重复输出开始处理的信息，因为在读取文件后已经有了类似的输出

for (var i = startPos; i < arr_a.length; i++) {
  if (!running) { // 检查是否暂停
    console.warn("暂停处理");
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length);
    console.warn("当前单号: " + arr_a[i]);
    console.warn("已保存当前进度: " + i); // 显示已保存的当前进度，用于断点继续

    // 在暂停时额外写入一次记录文件，确保暂停位置被准确记录
    files.append(jl_path, i + "\n");

    // 更新全局进度变量，用于断点继续
    currentProgress = i;
    break;
  }

  // 只在开始、每500条、暂停或结束时输出进度信息
  if (i > startPos && (i + 1) % 500 === 0) {
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length);
    console.warn("当前单号: " + arr_a[i]);
  }

  // 每次循环都重新查找UI元素
  var inputField = id("took_shipment_collection_bill_number").findOne(); // 查找单号输入框
  var addButton = id(UI_ELEMENTS.JIBAO.ADD_BUTTON).findOne(); // 查找添加按钮，使用预设ID

  // 设置单号并点击添加按钮
  inputField.setText(arr_a[i]); // 设置单号
  sleep(15); // 等待15ms，确保输入完成
  addButton.click(); // 点击添加按钮
  sleep(15); // 等待15ms，确保输入完成

  // 检查是否能找到输入框，如果找不到可能是弹出了确认对话框
  var nextInputField = id("took_shipment_collection_bill_number").findOne(1000); // 尝试1秒内找到输入框
  if (nextInputField === null) { // 如果找不到输入框
    // 可能是弹出了确认对话框，双击指定坐标关闭对话框
    click(581, 798); // 第一次点击
    sleep(50); // 等待50毫秒
    click(581, 798); // 第二次点击，形成双击
    sleep(100); // 等待对话框关闭

    // 不再尝试查找输入框，直接继续处理下一个单号
  }

  // 每处理500条或最后一条时，才更新进度文件，减少IO操作
  if ((i + 1) % 500 === 0 || i === arr_a.length - 1) {
    files.append(jl_path, i + "\n");
  }

  // 只在每500条时更新性能统计
  if (i > startPos && (i + 1) % 500 === 0) {
    updatePerformanceStats(i - startPos + 1);
  }

  // 内存清理仍然按时间间隔进行
  var now = Date.now();
  if (now - memoryManager.lastCleanTime >= memoryManager.cleanInterval) {
    cleanMemory();
  }
}

  // 处理完成后的操作
  // 只有当完成所有单号处理时才显示完成信息
  if (i >= arr_a.length) {
    console.warn("当前进度: " + arr_a.length + "/" + arr_a.length); // 输出完成进度
    console.warn("全部单号处理完成"); // 输出完成信息

    // 显示最终性能统计
    updatePerformanceStats(arr_a.length - startPos);
  }

  // 不重置暂停标记，确保下次启动时始终显示选择弹窗
  // hasBeenPaused = false; // 不再重置暂停标志
}

/**
 * 执行韵达到派脚本，处理单号文件
 * @param {string} filePath - 单号文件路径
 * @param {number} startIndex - 开始处理的索引位置，用于断点续传
 */
function runDaopaiScript(filePath, startIndex) {
  // 记录文件路径，用于保存处理进度
  var jl_path = "/sdcard/Pictures/记录文本.txt"; // 记录文件路径
  var jl_hs = 0; // 初始化记录行数为0

  // 创建或打开记录文件
  if (files.create(jl_path)) { // 如果文件不存在，创建新文件
    sleep(150); // 等待文件创建完成
  } else { // 如果文件已存在
    try {
      // 读取记录文件的所有行
      var arr_jl = open(jl_path).readlines(); // 读取文件内容
      // 获取最后一行的值作为起始位置，如果文件为空则从0开始
      jl_hs = arr_jl.length === 0 ? 0 : parseInt(arr_jl[arr_jl.length - 1]) + 1;
    } catch (e) {
      // 如果读取文件出错，重新创建文件
      files.remove(jl_path); // 删除可能损坏的文件
      files.create(jl_path); // 创建新文件
      jl_hs = 0; // 重置起始位置为0
    }
  }

  // 确定开始处理的位置：优先使用传入的起始索引，如果没有则使用记录文件中的进度
  var startPos = (startIndex !== undefined && startIndex > 0) ? startIndex : jl_hs;

  // 读取单号文件内容
  var arr_a = open(filePath).readlines(); // 读取单号文件的所有行
  console.warn("当前单号文本数量: " + arr_a.length); // 输出单号总数

  // 根据是否是断点继续显示不同的信息
  if (startIndex !== undefined && startIndex > 0) {
    console.warn("从已保存进度: " + startIndex + " 条继续处理"); // 显示从已保存进度继续
  } else {
    console.warn("从第 " + (startPos + 1) + " 条开始处理"); // 显示从头开始或从记录文件读取的进度开始
  }

  // 初始化性能统计
  var now = Date.now(); // 只获取一次当前时间，提高效率
  performanceStats.startTime = now;
  performanceStats.lastCheckTime = now;
  performanceStats.lastProcessedCount = startPos;
  performanceStats.processedCount = startPos;

  // 初始化内存管理
  memoryManager.lastCleanTime = now;

// 不再预先查找并缓存UI元素，而是在每次循环中重新查找
// 移除了处理单个单号的函数，直接在循环中处理

// 不需要额外的currentPos变量，因为已经有i作为循环索引

// 不再重复输出开始处理的信息，因为在读取文件后已经有了类似的输出

for (var i = startPos; i < arr_a.length; i++) {
  if (!running) { // 检查是否暂停
    console.warn("暂停处理");
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length);
    console.warn("当前单号: " + arr_a[i]);
    console.warn("已保存当前进度: " + i); // 显示已保存的当前进度，用于断点继续

    // 在暂停时额外写入一次记录文件，确保暂停位置被准确记录
    files.append(jl_path, i + "\n");

    // 更新全局进度变量，用于断点继续
    currentProgress = i;
    break;
  }

  // 只在开始、每500条、暂停或结束时输出进度信息
  if (i > startPos && (i + 1) % 500 === 0) {
    console.warn("当前进度: " + (i + 1) + "/" + arr_a.length);
    console.warn("当前单号: " + arr_a[i]);
  }

  // 每次循环都重新查找UI元素
  var inputField = id("shipment_business_shipment_give_shipment_bill_number").findOne(); // 查找单号输入框
  var addButton = id(UI_ELEMENTS.DAOPAI.ADD_BUTTON).findOne(); // 查找添加按钮，使用预设ID

  // 设置单号并点击添加按钮
  inputField.setText(arr_a[i]); // 设置单号
  sleep(15); // 等待15ms，确保输入完成
  addButton.click(); // 点击添加按钮
  sleep(15); // 等待15ms，确保输入完成

  // 每处理500条或最后一条时，才更新进度文件，减少IO操作
  if ((i + 1) % 500 === 0 || i === arr_a.length - 1) {
    files.append(jl_path, i + "\n");
  }

  // 只在每500条时更新性能统计
  if (i > startPos && (i + 1) % 500 === 0) {
    updatePerformanceStats(i - startPos + 1);
  }

  // 内存清理仍然按时间间隔进行
  var now = Date.now();
  if (now - memoryManager.lastCleanTime >= memoryManager.cleanInterval) {
    cleanMemory();
  }
}

  // 处理完成后的操作
  // 只有当完成所有单号处理时才显示完成信息
  if (i >= arr_a.length) {
    console.warn("当前进度: " + arr_a.length + "/" + arr_a.length); // 输出完成进度
    console.warn("全部单号处理完成"); // 输出完成信息

    // 显示最终性能统计
    updatePerformanceStats(arr_a.length - startPos);
  }

  // 不重置暂停标记，确保下次启动时始终显示选择弹窗
  // hasBeenPaused = false; // 不再重置暂停标志
}

