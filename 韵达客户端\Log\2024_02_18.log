﻿#Software: 韵达自动化扫描客户端
#ProcessID: 10552 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,696M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-18
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:10:12.168 15 Y P KuangScannerNet 准备开始监听4个服务器
16:10:12.175 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:10:12.176 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:10:12.179 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:10:12.179 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:10:12.180 15 Y P KuangScannerNet 准备就绪！
16:10:12.210 15 Y P Http 准备开始监听2个服务器
16:10:12.226 15 Y P Http 开始监听 tcp://0.0.0.0:8080
16:10:12.227 15 Y P Http 开始监听 tcp://[::]:8080
16:10:12.227 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 15884 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,771M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-18
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:14:45.924 15 Y P KuangScannerNet 准备开始监听4个服务器
16:14:45.995 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:14:46.012 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:14:46.016 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:14:46.028 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:14:46.028 15 Y P KuangScannerNet 准备就绪！
16:14:46.041 15 Y P Http 准备开始监听2个服务器
16:14:46.042 15 Y P Http 开始监听 tcp://0.0.0.0:8080
16:14:46.042 15 Y P Http 开始监听 tcp://[::]:8080
16:14:46.042 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 16852 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,694M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-02-18
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
16:41:08.818  5 Y P KuangScannerNet 准备开始监听4个服务器
16:41:08.855  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
16:41:08.870  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
16:41:08.878  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
16:41:08.909  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
16:41:08.910  5 Y P KuangScannerNet 准备就绪！
16:41:08.925  5 Y P Http 准备开始监听2个服务器
16:41:08.940  5 Y P Http 开始监听 tcp://0.0.0.0:8080
16:41:08.960  5 Y P Http 开始监听 tcp://[::]:8080
16:41:08.960  5 Y P Http 准备就绪！
