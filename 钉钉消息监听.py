from pywinauto import Desktop
import time

# 存储历史消息内容，避免重复
seen_messages = set()

# 获取钉钉窗口
app = Desktop(backend="uia")
dingtalk_window = app.window(title_re=".*钉钉.*", control_type="Window")

# 定位消息框（你提供的路径下最核心的一层 Group 控件）
message_area = dingtalk_window.child_window(control_type="Group", found_index=1)

def get_messages():
    global seen_messages
    new_messages = []

    # 查找所有子项中的文本控件（Text），即为聊天气泡
    text_items = message_area.descendants(control_type="Text")

    for item in text_items:
        try:
            content = item.window_text().strip()
            if content and content not in seen_messages:
                new_messages.append(content)
                seen_messages.add(content)
        except Exception:
            continue

    return new_messages

# 轮询读取最新消息
print("开始监听钉钉消息框...")
while True:
    messages = get_messages()
    if messages:
        print("💬 新消息：")
        for msg in messages:
            print(" -", msg)
    time.sleep(5)  # 每5秒扫描一次
