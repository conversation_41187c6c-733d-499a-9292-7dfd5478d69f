#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解密分析脚本 - 乡镇驿站扫描数据
"""

import base64
import json
import hashlib
import binascii

def analyze_encrypted_data(encrypted_data: str):
    """分析加密数据的特征"""
    print("=== 加密数据分析 ===")
    print(f"原始数据: {encrypted_data}")
    print(f"数据长度: {len(encrypted_data)}")
    print(f"是否包含Base64字符: {all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in encrypted_data)}")
    
    try:
        # 尝试Base64解码
        decoded_data = base64.b64decode(encrypted_data)
        print(f"Base64解码成功，解码后长度: {len(decoded_data)}")
        print(f"解码后数据(hex): {decoded_data.hex()}")
        print(f"解码后数据(前32字节): {decoded_data[:32].hex()}")
        return decoded_data
    except Exception as e:
        print(f"Base64解码失败: {e}")
        return None

def try_simple_xor_decrypt(encrypted_data: bytes, key: str):
    """尝试简单XOR解密"""
    try:
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)

        decrypted = bytearray()
        for i, byte in enumerate(encrypted_data):
            decrypted.append(byte ^ key_bytes[i % key_len])

        try:
            result_str = decrypted.decode('utf-8')
            print(f"XOR解密成功，密钥: {key}")
            print(f"解密结果: {result_str}")
            return result_str
        except:
            print(f"XOR解密失败，无法UTF-8解码")
            return None

    except Exception as e:
        print(f"XOR解密失败: {e}")
        return None

def analyze_data_patterns(data: bytes):
    """分析数据模式"""
    print("\n=== 数据模式分析 ===")

    # 检查是否有重复的16字节块（ECB模式特征）
    blocks = [data[i:i+16] for i in range(0, len(data), 16)]
    unique_blocks = set(blocks)

    print(f"总块数: {len(blocks)}")
    print(f"唯一块数: {len(unique_blocks)}")

    if len(blocks) != len(unique_blocks):
        print("⚠️  发现重复块，可能使用ECB模式")
    else:
        print("✓ 无重复块，可能使用CBC或其他模式")

    # 分析前几个字节
    print(f"前16字节: {data[:16].hex()}")
    print(f"第17-32字节: {data[16:32].hex()}")

    # 检查是否有明显的模式
    if data[:16] == data[16:32]:
        print("⚠️  前两个块相同，强烈建议ECB模式")

def try_decode_without_crypto(encrypted_data: bytes):
    """不使用加密库的解码尝试"""
    print("\n=== 无加密库解码尝试 ===")

    # 尝试直接UTF-8解码
    try:
        direct_decode = encrypted_data.decode('utf-8')
        print(f"直接UTF-8解码成功: {direct_decode}")
        return direct_decode
    except:
        print("直接UTF-8解码失败")

    # 尝试不同编码
    encodings = ['latin-1', 'cp1252', 'iso-8859-1']
    for encoding in encodings:
        try:
            decoded = encrypted_data.decode(encoding)
            print(f"{encoding}解码成功: {decoded[:100]}...")
            return decoded
        except:
            continue

    print("所有编码尝试失败")
    return None

def generate_possible_keys():
    """生成可能的密钥"""
    keys = []

    # 从伪代码中发现的真实密钥！
    keys.extend([
        "k301qsjbrh1s6ega",  # 从伪代码第9756行发现的AES密钥
    ])

    # 基于分析结果的可能密钥
    keys.extend([
        "530001",  # 分拨中心代码
        "yunda",   # 韵达
        "yundasys", # 韵达系统
        "scan",    # 扫描
        "rural",   # 乡镇
        "station", # 驿站
        "1234567890123456",  # 16位数字
        "abcdef1234567890",  # 16位混合
        "yunda530001scan",   # 组合密钥
        "530001yunda1234",   # 组合密钥
    ])

    # 网络代码 + 员工ID + 密码 + 分拨中心 + 设备SN的组合
    keys.extend([
        "net001emp001pwd001530001dev001",
        "530001123456789012345678901234",
        "yundascan530001",
        "scan530001yunda",
    ])

    return keys

def main():
    """主函数"""
    encrypted_data = "55sxtMD6giHsi9t4D3miHbqvN7+HBM9olSpT7sJktxWCO0vhj+VcG0Jznf30zXaQXhQRf04wWTWMH6b0+9pWBHwgZ39oIdO2VHmDl15b/CmZ00Gh2jFkrtZ+ujfEIbh1bf9AMFLmvGqGQ3zcGfEfReqxiLScBKt7EDiCxMkvWyHPSA0eMgRWaKlJ1oRZ+1fXCXGfRHt49W24p04HlcVEY2Yum5ZUY8VgH+UKGOPWU2UXvql91JxaDEcKrrv68r3faqjlqoTAkWxw6bGi3yj6gp24dVLiKMfjrlLaarDRWdtu8xdLnSx8YSzxmnPJFZm107HmRPctM/44aP2f/P3HwdJKFjdBBjJgAj0OYjfp6ahbmYFlH3FXt8LMgJyuWZeN"
    
    print("乡镇驿站扫描数据解密分析")
    print("=" * 50)
    
    # 分析加密数据
    decoded_data = analyze_encrypted_data(encrypted_data)
    
    if decoded_data is None:
        print("无法进行Base64解码，退出")
        return
    
    # 分析数据模式
    analyze_data_patterns(decoded_data)

    # 尝试无加密库解码
    result = try_decode_without_crypto(decoded_data)
    if result:
        print("✓ 无加密库解码成功!")
        return

    print("\n=== 尝试XOR解密 ===")

    # 生成可能的密钥
    possible_keys = generate_possible_keys()

    success = False
    for key in possible_keys:
        print(f"\n--- 尝试XOR密钥: {key} ---")

        result = try_simple_xor_decrypt(decoded_data, key)
        if result and len(result) > 10:
            print("✓ XOR解密可能成功!")
            success = True
            break

    if not success:
        print("\n❌ 使用预设密钥解密失败")
        print("建议:")
        print("1. 需要安装pycryptodome库进行AES解密: pip install pycryptodome")
        print("2. 检查伪代码中的密钥生成逻辑")
        print("3. 分析网络请求中的认证信息")
        print("4. 查找设备序列号和员工ID")
        print("5. 尝试其他加密算法")

        # 显示原始数据的十六进制表示
        print(f"\n原始加密数据(hex前64字节): {decoded_data[:64].hex()}")
        print(f"数据总长度: {len(decoded_data)} 字节")

if __name__ == "__main__":
    main()
