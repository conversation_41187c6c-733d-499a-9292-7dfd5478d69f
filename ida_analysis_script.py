#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDA Analysis Script for Logistics System
Analyzes courier tracking number validation and encryption
"""

import re
import json
from typing import Dict, List, Tuple, Optional

class LogisticsAnalyzer:
    def __init__(self, pseudocode_file: str):
        self.pseudocode_file = pseudocode_file
        self.validation_patterns = []
        self.encryption_patterns = []
        self.network_patterns = []
        self.device_patterns = []
        
    def load_pseudocode(self) -> str:
        """Load pseudocode from file"""
        try:
            with open(self.pseudocode_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error loading file: {e}")
            return ""
    
    def analyze_validation_logic(self, content: str) -> List[Dict]:
        """Analyze tracking number validation patterns"""
        patterns = []
        
        # Pattern 1: Length validation (4 digits for 530001 prefix)
        length_pattern = r'if.*length.*[<>=!].*4|if.*4.*[<>=!].*length|if.*len.*[<>=!].*4|if.*4.*[<>=!].*len|strlen.*4'
        matches = re.finditer(length_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'length_validation',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Validates tracking number length (4 digits after 530001 prefix)'
            })
        
        # Pattern 2: First digit validation (starts with 9)
        first_digit_pattern = r'if.*\[0\].*[=!].*9|if.*9.*[=!].*\[0\]|if.*first.*[=!].*9|if.*9.*[=!].*first|\*.*==.*57|\*.*!=.*57'
        matches = re.finditer(first_digit_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'first_digit_validation',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Validates first digit is 9 (ASCII 57)'
            })
        
        # Pattern 3: 530001 prefix validation
        prefix_pattern = r'530001|53.*00.*01'
        matches = re.finditer(prefix_pattern, content)
        for match in matches:
            patterns.append({
                'type': 'prefix_validation',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Validates 530001 prefix'
            })
        
        # Pattern 4: Complete tracking number format validation
        format_pattern = r'if.*length.*!=.*4.*\|\||if.*4.*!=.*\|\||if.*\[0\].*!=.*9.*\|\|'
        matches = re.finditer(format_pattern, content)
        for match in matches:
            patterns.append({
                'type': 'format_validation',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Complete tracking number format validation (530001 + 9XXX)'
            })
        
        return patterns
    
    def analyze_encryption_logic(self, content: str) -> List[Dict]:
        """Analyze encryption and decryption patterns"""
        patterns = []
        
        # AES encryption patterns
        aes_pattern = r'AES[-_]?ECB|AES[-_]?CBC|AES[-_]?encryption'
        matches = re.finditer(aes_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'aes_encryption',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'AES encryption implementation'
            })
        
        # Base64 encoding patterns
        base64_pattern = r'base64|b64encode|b64decode'
        matches = re.finditer(base64_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'base64_encoding',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Base64 encoding/decoding'
            })
        
        # Key generation patterns
        key_pattern = r'key.*generation|generate.*key|crypto.*key'
        matches = re.finditer(key_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'key_generation',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Cryptographic key generation'
            })
        
        return patterns
    
    def analyze_network_requests(self, content: str) -> List[Dict]:
        """Analyze network request patterns"""
        patterns = []
        
        # HTTP request patterns
        http_pattern = r'HTTP|POST|GET|PUT|DELETE|Content-Type|User-Agent'
        matches = re.finditer(http_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'http_request',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'HTTP request/response handling'
            })
        
        # Token patterns
        token_pattern = r'\[token\]|token:|authorization'
        matches = re.finditer(token_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'authentication_token',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Authentication token handling'
            })
        
        return patterns
    
    def analyze_device_info(self, content: str) -> List[Dict]:
        """Analyze device and account information patterns"""
        patterns = []
        
        # Device SN patterns
        sn_pattern = r'device.*SN|SN.*device|serial.*number'
        matches = re.finditer(sn_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'device_sn',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Device serial number handling'
            })
        
        # Employee ID patterns
        employee_pattern = r'employee.*ID|ID.*employee|worker.*ID'
        matches = re.finditer(employee_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'employee_id',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Employee ID handling'
            })
        
        # Password patterns
        password_pattern = r'password|passwd|pwd|credential'
        matches = re.finditer(password_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'password',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Password/credential handling'
            })
        
        return patterns
    
    def extract_context(self, content: str, position: int, context_size: int = 200) -> str:
        """Extract context around a pattern match"""
        start = max(0, position - context_size)
        end = min(len(content), position + context_size)
        return content[start:end]
    
    def find_validation_functions(self, content: str) -> List[Dict]:
        """Find specific validation function implementations"""
        functions = []
        
        # Look for functions that validate tracking numbers
        function_pattern = r'(sub_[0-9A-F]+|function_[0-9A-F]+).*\{[^}]*(?:length|strlen|9|57|530001)[^}]*\}'
        matches = re.finditer(function_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in matches:
            functions.append({
                'type': 'validation_function',
                'function_name': match.group(1) if match.groups() else 'unknown',
                'code': match.group(),
                'position': match.start(),
                'description': 'Function implementing tracking number validation'
            })
        
        return functions
    
    def analyze_authentication_format(self, content: str) -> List[Dict]:
        """Analyze authentication format patterns"""
        patterns = []
        
        # Look for authentication format: network_code + employee_id + password + distribution_center + device_sn
        auth_pattern = r'network.*code|employee.*id|distribution.*center|device.*sn'
        matches = re.finditer(auth_pattern, content, re.IGNORECASE)
        
        for match in matches:
            patterns.append({
                'type': 'authentication_format',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Authentication format component'
            })
        
        return patterns
    
    def generate_tracking_number_decoder(self) -> str:
        """Generate Python code to decode tracking numbers"""
        decoder_code = '''
def decode_tracking_number(encrypted_number: str) -> dict:
    """
    Decode encrypted tracking number based on analysis
    Format: 530001 + 9XXX (where XXX are 3 additional digits)
    """
    result = {
        'valid': False,
        'prefix': None,
        'suffix': None,
        'full_number': None,
        'errors': []
    }
    
    try:
        # Check if number starts with 530001
        if not encrypted_number.startswith('530001'):
            result['errors'].append('Invalid prefix - must start with 530001')
            return result
        
        # Extract the suffix (should be 4 digits starting with 9)
        suffix = encrypted_number[6:]  # Remove 530001 prefix
        
        if len(suffix) != 4:
            result['errors'].append(f'Invalid suffix length - expected 4 digits, got {len(suffix)}')
            return result
        
        if not suffix[0] == '9':
            result['errors'].append('Invalid first digit - must start with 9')
            return result
        
        if not suffix.isdigit():
            result['errors'].append('Suffix must contain only digits')
            return result
        
        result['valid'] = True
        result['prefix'] = '530001'
        result['suffix'] = suffix
        result['full_number'] = encrypted_number
        
    except Exception as e:
        result['errors'].append(f'Decoding error: {str(e)}')
    
    return result

def generate_valid_tracking_numbers(count: int = 10) -> list:
    """Generate valid tracking numbers for testing"""
    numbers = []
    for i in range(count):
        # Generate 3 random digits after 9
        suffix_digits = str(i).zfill(3)[-3:]  # Ensure 3 digits
        tracking_number = f"5300019{suffix_digits}"
        numbers.append(tracking_number)
    return numbers
'''
        return decoder_code

    def run_analysis(self) -> Dict:
        """Run complete analysis on the pseudocode"""
        print("Loading pseudocode...")
        content = self.load_pseudocode()

        if not content:
            return {'error': 'Failed to load pseudocode file'}

        print("Analyzing validation logic...")
        validation_patterns = self.analyze_validation_logic(content)

        print("Analyzing encryption logic...")
        encryption_patterns = self.analyze_encryption_logic(content)

        print("Analyzing network requests...")
        network_patterns = self.analyze_network_requests(content)

        print("Analyzing device information...")
        device_patterns = self.analyze_device_info(content)

        print("Finding validation functions...")
        validation_functions = self.find_validation_functions(content)

        print("Analyzing authentication format...")
        auth_patterns = self.analyze_authentication_format(content)

        # Compile results
        results = {
            'summary': {
                'total_patterns': len(validation_patterns) + len(encryption_patterns) +
                                len(network_patterns) + len(device_patterns),
                'validation_patterns': len(validation_patterns),
                'encryption_patterns': len(encryption_patterns),
                'network_patterns': len(network_patterns),
                'device_patterns': len(device_patterns),
                'validation_functions': len(validation_functions),
                'auth_patterns': len(auth_patterns)
            },
            'validation_logic': validation_patterns,
            'encryption_logic': encryption_patterns,
            'network_requests': network_patterns,
            'device_info': device_patterns,
            'validation_functions': validation_functions,
            'authentication_format': auth_patterns,
            'decoder_code': self.generate_tracking_number_decoder()
        }

        return results

    def generate_report(self, results: Dict) -> str:
        """Generate a comprehensive analysis report"""
        report = []
        report.append("=" * 80)
        report.append("IDA ANALYSIS REPORT - LOGISTICS SYSTEM")
        report.append("=" * 80)
        report.append("")

        # Summary
        summary = results.get('summary', {})
        report.append("ANALYSIS SUMMARY:")
        report.append("-" * 40)
        report.append(f"Total patterns found: {summary.get('total_patterns', 0)}")
        report.append(f"Validation patterns: {summary.get('validation_patterns', 0)}")
        report.append(f"Encryption patterns: {summary.get('encryption_patterns', 0)}")
        report.append(f"Network patterns: {summary.get('network_patterns', 0)}")
        report.append(f"Device patterns: {summary.get('device_patterns', 0)}")
        report.append(f"Validation functions: {summary.get('validation_functions', 0)}")
        report.append(f"Authentication patterns: {summary.get('auth_patterns', 0)}")
        report.append("")

        # Tracking Number Validation Analysis
        report.append("TRACKING NUMBER VALIDATION ANALYSIS:")
        report.append("-" * 50)
        report.append("Based on the analysis, the tracking number format appears to be:")
        report.append("  Format: 530001 + 9XXX")
        report.append("  - Prefix: 530001 (6 digits)")
        report.append("  - Suffix: 9XXX (4 digits starting with 9)")
        report.append("  - Total length: 10 digits")
        report.append("")

        validation_patterns = results.get('validation_logic', [])
        if validation_patterns:
            report.append("Validation patterns found:")
            for i, pattern in enumerate(validation_patterns[:5], 1):  # Show first 5
                report.append(f"  {i}. {pattern['type']}: {pattern['description']}")
                report.append(f"     Pattern: {pattern['pattern'][:100]}...")
        report.append("")

        # Encryption Analysis
        encryption_patterns = results.get('encryption_logic', [])
        if encryption_patterns:
            report.append("ENCRYPTION ANALYSIS:")
            report.append("-" * 30)
            for pattern in encryption_patterns[:3]:  # Show first 3
                report.append(f"- {pattern['type']}: {pattern['description']}")
        report.append("")

        # Network Analysis
        network_patterns = results.get('network_requests', [])
        if network_patterns:
            report.append("NETWORK COMMUNICATION ANALYSIS:")
            report.append("-" * 40)
            for pattern in network_patterns[:3]:  # Show first 3
                report.append(f"- {pattern['type']}: {pattern['description']}")
        report.append("")

        # Authentication Format
        auth_patterns = results.get('authentication_format', [])
        if auth_patterns:
            report.append("AUTHENTICATION FORMAT ANALYSIS:")
            report.append("-" * 40)
            report.append("The system appears to use the following authentication components:")
            for pattern in auth_patterns:
                report.append(f"- {pattern['description']}")
        report.append("")

        # Decoder Implementation
        report.append("TRACKING NUMBER DECODER:")
        report.append("-" * 35)
        report.append("A Python decoder has been generated based on the analysis.")
        report.append("Key features:")
        report.append("- Validates 530001 prefix")
        report.append("- Checks 4-digit suffix starting with 9")
        report.append("- Provides detailed error reporting")
        report.append("- Includes test number generation")
        report.append("")

        # Recommendations
        report.append("RECOMMENDATIONS:")
        report.append("-" * 20)
        report.append("1. Use the generated decoder to validate tracking numbers")
        report.append("2. Test with known valid/invalid numbers")
        report.append("3. Monitor network traffic for encryption keys")
        report.append("4. Analyze authentication token generation")
        report.append("5. Reverse engineer password conversion logic")
        report.append("")

        report.append("=" * 80)
        report.append("END OF REPORT")
        report.append("=" * 80)

        return "\n".join(report)

    def save_results(self, results: Dict, output_file: str = "analysis_results.json"):
        """Save analysis results to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"Results saved to {output_file}")
        except Exception as e:
            print(f"Error saving results: {e}")

    def save_report(self, report: str, output_file: str = "analysis_report.txt"):
        """Save analysis report to text file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"Report saved to {output_file}")
        except Exception as e:
            print(f"Error saving report: {e}")

def main():
    """Main execution function"""
    print("IDA Analysis Script for Logistics System")
    print("=" * 50)

    # Initialize analyzer
    analyzer = LogisticsAnalyzer("all_pseudocode_output.txt")

    # Run analysis
    results = analyzer.run_analysis()

    if 'error' in results:
        print(f"Error: {results['error']}")
        return

    # Generate and display report
    report = analyzer.generate_report(results)
    print(report)

    # Save results
    analyzer.save_results(results)
    analyzer.save_report(report)

    # Generate decoder file
    decoder_code = results.get('decoder_code', '')
    if decoder_code:
        try:
            with open('tracking_number_decoder.py', 'w', encoding='utf-8') as f:
                f.write(decoder_code)
            print("Decoder saved to tracking_number_decoder.py")
        except Exception as e:
            print(f"Error saving decoder: {e}")

if __name__ == "__main__":
    main()
