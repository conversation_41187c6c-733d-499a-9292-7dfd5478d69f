#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDA Analysis Script for Logistics System
Analyzes courier tracking number validation and business logic
Based on real business requirements:
1. 派件到达: 固定设置分拨中心代码 530001
2. 到派一体: 单号输入时长度等于4的代码
3. 乡镇驿站扫描: 单号输入时长度等于4的代码
4. 集包扫描: 单号输入时开头为9的代码 + 下一站为预设6位数代码
"""

import re
import json
from typing import Dict, List, Tuple, Optional

class LogisticsAnalyzer:
    def __init__(self, pseudocode_file: str):
        self.pseudocode_file = pseudocode_file
        self.business_patterns = []
        self.api_endpoints = []
        self.validation_logic = []
        self.authentication_format = []

    def load_pseudocode(self) -> str:
        """Load pseudocode from file"""
        try:
            with open(self.pseudocode_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error loading file: {e}")
            return ""
    
    def analyze_business_logic(self, content: str) -> List[Dict]:
        """Analyze business logic patterns based on real requirements"""
        patterns = []

        # 1. 派件到达 - 固定设置分拨中心代码 530001
        delivery_pattern = r'530001'
        matches = re.finditer(delivery_pattern, content)
        for match in matches:
            context = self.extract_context(content, match.start(), 100)
            patterns.append({
                'type': 'delivery_arrival',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '派件到达 - 固定设置分拨中心代码 530001'
            })

        # 2. 到派一体 & 乡镇驿站扫描 - 长度等于4的代码验证
        length_4_pattern = r'if.*length.*==.*4|if.*4.*==.*length|if.*len.*==.*4|if.*4.*==.*len|strlen.*==.*4'
        matches = re.finditer(length_4_pattern, content, re.IGNORECASE)
        for match in matches:
            context = self.extract_context(content, match.start(), 150)
            patterns.append({
                'type': 'length_4_validation',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '到派一体/乡镇驿站扫描 - 单号输入时长度等于4的代码验证'
            })

        # 3. 集包扫描 - 开头为9的代码检查
        starts_with_9_pattern = r'if.*\[0\].*==.*9|if.*9.*==.*\[0\]|if.*\*.*==.*57|if.*57.*==.*\*'
        matches = re.finditer(starts_with_9_pattern, content, re.IGNORECASE)
        for match in matches:
            context = self.extract_context(content, match.start(), 150)
            patterns.append({
                'type': 'starts_with_9_validation',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '集包扫描 - 单号输入时开头为9的代码检查'
            })

        # 4. 6位数代码检查 (下一站预设代码)
        six_digit_pattern = r'length.*==.*6|6.*==.*length|len.*==.*6|6.*==.*len|strlen.*==.*6'
        matches = re.finditer(six_digit_pattern, content, re.IGNORECASE)
        for match in matches:
            context = self.extract_context(content, match.start(), 150)
            patterns.append({
                'type': 'six_digit_validation',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '集包扫描 - 下一站为预设6位数代码验证'
            })

        return patterns
    
    def analyze_api_endpoints(self, content: str) -> List[Dict]:
        """Analyze API endpoints and network communication"""
        patterns = []

        # Yunda API endpoints
        api_pattern = r'http://scan\.yundasys\.com[^"\']*'
        matches = re.finditer(api_pattern, content)
        for match in matches:
            context = self.extract_context(content, match.start(), 200)
            patterns.append({
                'type': 'api_endpoint',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '韵达系统API端点'
            })

        # Token patterns
        token_pattern = r'\[token\]|token:|authorization'
        matches = re.finditer(token_pattern, content, re.IGNORECASE)
        for match in matches:
            context = self.extract_context(content, match.start(), 100)
            patterns.append({
                'type': 'authentication_token',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': '认证令牌处理'
            })

        # HTTP headers
        header_pattern = r'Content-Type|User-Agent|application/octet-stream'
        matches = re.finditer(header_pattern, content, re.IGNORECASE)
        for match in matches:
            context = self.extract_context(content, match.start(), 100)
            patterns.append({
                'type': 'http_header',
                'pattern': match.group(),
                'position': match.start(),
                'context': context,
                'description': 'HTTP请求头设置'
            })

        return patterns
    
    def analyze_network_requests(self, content: str) -> List[Dict]:
        """Analyze network request patterns"""
        patterns = []
        
        # HTTP request patterns
        http_pattern = r'HTTP|POST|GET|PUT|DELETE|Content-Type|User-Agent'
        matches = re.finditer(http_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'http_request',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'HTTP request/response handling'
            })
        
        # Token patterns
        token_pattern = r'\[token\]|token:|authorization'
        matches = re.finditer(token_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'authentication_token',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Authentication token handling'
            })
        
        return patterns
    
    def analyze_device_info(self, content: str) -> List[Dict]:
        """Analyze device and account information patterns"""
        patterns = []
        
        # Device SN patterns
        sn_pattern = r'device.*SN|SN.*device|serial.*number'
        matches = re.finditer(sn_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'device_sn',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Device serial number handling'
            })
        
        # Employee ID patterns
        employee_pattern = r'employee.*ID|ID.*employee|worker.*ID'
        matches = re.finditer(employee_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'employee_id',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Employee ID handling'
            })
        
        # Password patterns
        password_pattern = r'password|passwd|pwd|credential'
        matches = re.finditer(password_pattern, content, re.IGNORECASE)
        for match in matches:
            patterns.append({
                'type': 'password',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Password/credential handling'
            })
        
        return patterns
    
    def extract_context(self, content: str, position: int, context_size: int = 200) -> str:
        """Extract context around a pattern match"""
        start = max(0, position - context_size)
        end = min(len(content), position + context_size)
        return content[start:end]
    
    def find_validation_functions(self, content: str) -> List[Dict]:
        """Find specific validation function implementations"""
        functions = []
        
        # Look for functions that validate tracking numbers
        function_pattern = r'(sub_[0-9A-F]+|function_[0-9A-F]+).*\{[^}]*(?:length|strlen|9|57|530001)[^}]*\}'
        matches = re.finditer(function_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in matches:
            functions.append({
                'type': 'validation_function',
                'function_name': match.group(1) if match.groups() else 'unknown',
                'code': match.group(),
                'position': match.start(),
                'description': 'Function implementing tracking number validation'
            })
        
        return functions
    
    def analyze_authentication_format(self, content: str) -> List[Dict]:
        """Analyze authentication format patterns"""
        patterns = []
        
        # Look for authentication format: network_code + employee_id + password + distribution_center + device_sn
        auth_pattern = r'network.*code|employee.*id|distribution.*center|device.*sn'
        matches = re.finditer(auth_pattern, content, re.IGNORECASE)
        
        for match in matches:
            patterns.append({
                'type': 'authentication_format',
                'pattern': match.group(),
                'position': match.start(),
                'description': 'Authentication format component'
            })
        
        return patterns
    
    def generate_business_logic_script(self) -> str:
        """Generate Python script for business logic validation"""
        script_code = '''
def validate_delivery_arrival(distribution_center_code: str) -> dict:
    """
    派件到达验证 - 固定设置分拨中心代码 530001
    """
    result = {
        'valid': distribution_center_code == '530001',
        'code': distribution_center_code,
        'type': 'delivery_arrival',
        'description': '派件到达'
    }
    if not result['valid']:
        result['error'] = f'分拨中心代码错误，应为530001，实际为{distribution_center_code}'
    return result

def validate_dispatch_integration(input_code: str) -> dict:
    """
    到派一体验证 - 单号输入时长度等于4的代码
    """
    result = {
        'valid': len(input_code) == 4 and input_code.isdigit(),
        'code': input_code,
        'type': 'dispatch_integration',
        'description': '到派一体'
    }
    if not result['valid']:
        if len(input_code) != 4:
            result['error'] = f'代码长度错误，应为4位，实际为{len(input_code)}位'
        elif not input_code.isdigit():
            result['error'] = '代码必须为纯数字'
    return result

def validate_rural_station_scan(input_code: str) -> dict:
    """
    乡镇驿站扫描验证 - 单号输入时长度等于4的代码
    """
    result = {
        'valid': len(input_code) == 4 and input_code.isdigit(),
        'code': input_code,
        'type': 'rural_station_scan',
        'description': '乡镇驿站扫描'
    }
    if not result['valid']:
        if len(input_code) != 4:
            result['error'] = f'代码长度错误，应为4位，实际为{len(input_code)}位'
        elif not input_code.isdigit():
            result['error'] = '代码必须为纯数字'
    return result

def validate_package_collection(tracking_number: str, next_station_code: str) -> dict:
    """
    集包扫描验证 - 单号开头为9 + 下一站为预设6位数代码
    """
    result = {
        'valid': False,
        'tracking_number': tracking_number,
        'next_station_code': next_station_code,
        'type': 'package_collection',
        'description': '集包扫描',
        'errors': []
    }

    # 检查单号是否以9开头
    if not tracking_number.startswith('9'):
        result['errors'].append('单号必须以9开头')

    # 检查下一站代码是否为6位数字
    if len(next_station_code) != 6:
        result['errors'].append(f'下一站代码长度错误，应为6位，实际为{len(next_station_code)}位')
    elif not next_station_code.isdigit():
        result['errors'].append('下一站代码必须为纯数字')

    result['valid'] = len(result['errors']) == 0
    return result

def test_tracking_numbers():
    """测试真实的快递单号"""
    test_numbers = ['312799099115886', '434640497727147']

    print("=== 真实快递单号测试 ===")
    for number in test_numbers:
        print(f"\\n单号: {number}")
        print(f"长度: {len(number)}")
        print(f"首位: {number[0]}")
        print(f"是否以9开头: {number.startswith('9')}")

        # 测试集包扫描验证
        result = validate_package_collection(number, '530001')
        print(f"集包扫描验证: {'通过' if result['valid'] else '失败'}")
        if result['errors']:
            print(f"错误: {', '.join(result['errors'])}")

def test_business_logic():
    """测试所有业务逻辑"""
    print("=== 业务逻辑验证测试 ===")

    # 测试派件到达
    print("\\n1. 派件到达测试:")
    test_cases = ['530001', '530002', '123456']
    for code in test_cases:
        result = validate_delivery_arrival(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试到派一体
    print("\\n2. 到派一体测试:")
    test_cases = ['1234', '12345', 'abcd', '123']
    for code in test_cases:
        result = validate_dispatch_integration(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试乡镇驿站扫描
    print("\\n3. 乡镇驿站扫描测试:")
    test_cases = ['5678', '567', '56789', 'abcd']
    for code in test_cases:
        result = validate_rural_station_scan(code)
        print(f"  {code}: {'通过' if result['valid'] else '失败'}")
        if 'error' in result:
            print(f"    错误: {result['error']}")

    # 测试集包扫描
    print("\\n4. 集包扫描测试:")
    test_cases = [
        ('912345678901234', '530001'),
        ('812345678901234', '530001'),
        ('912345678901234', '53001'),
        ('912345678901234', 'abcdef')
    ]
    for tracking, station in test_cases:
        result = validate_package_collection(tracking, station)
        print(f"  {tracking[:10]}... + {station}: {'通过' if result['valid'] else '失败'}")
        if result['errors']:
            print(f"    错误: {', '.join(result['errors'])}")
'''
        return script_code

    def run_analysis(self) -> Dict:
        """Run complete analysis on the pseudocode"""
        print("Loading pseudocode...")
        content = self.load_pseudocode()

        if not content:
            return {'error': 'Failed to load pseudocode file'}

        print("Analyzing business logic patterns...")
        business_patterns = self.analyze_business_logic(content)

        print("Analyzing API endpoints...")
        api_patterns = self.analyze_api_endpoints(content)

        print("Analyzing network requests...")
        network_patterns = self.analyze_network_requests(content)

        print("Analyzing device information...")
        device_patterns = self.analyze_device_info(content)

        print("Finding validation functions...")
        validation_functions = self.find_validation_functions(content)

        print("Analyzing authentication format...")
        auth_patterns = self.analyze_authentication_format(content)

        # Compile results
        results = {
            'summary': {
                'total_patterns': len(business_patterns) + len(api_patterns) +
                                len(network_patterns) + len(device_patterns),
                'business_patterns': len(business_patterns),
                'api_patterns': len(api_patterns),
                'network_patterns': len(network_patterns),
                'device_patterns': len(device_patterns),
                'validation_functions': len(validation_functions),
                'auth_patterns': len(auth_patterns)
            },
            'business_logic': business_patterns,
            'api_endpoints': api_patterns,
            'network_requests': network_patterns,
            'device_info': device_patterns,
            'validation_functions': validation_functions,
            'authentication_format': auth_patterns,
            'business_script': self.generate_business_logic_script()
        }

        return results

    def generate_report(self, results: Dict) -> str:
        """Generate a comprehensive analysis report"""
        report = []
        report.append("=" * 80)
        report.append("IDA ANALYSIS REPORT - 物流系统业务逻辑分析")
        report.append("=" * 80)
        report.append("")

        # Summary
        summary = results.get('summary', {})
        report.append("分析摘要:")
        report.append("-" * 40)
        report.append(f"总模式数量: {summary.get('total_patterns', 0)}")
        report.append(f"业务逻辑模式: {summary.get('business_patterns', 0)}")
        report.append(f"API端点模式: {summary.get('api_patterns', 0)}")
        report.append(f"网络通信模式: {summary.get('network_patterns', 0)}")
        report.append(f"设备信息模式: {summary.get('device_patterns', 0)}")
        report.append(f"验证函数: {summary.get('validation_functions', 0)}")
        report.append(f"认证模式: {summary.get('auth_patterns', 0)}")
        report.append("")

        # Business Logic Analysis
        report.append("业务逻辑分析:")
        report.append("-" * 50)
        report.append("基于分析发现的业务逻辑规则:")
        report.append("")
        report.append("1. 派件到达:")
        report.append("   - 固定设置分拨中心代码: 530001")
        report.append("   - 用于标识包裹到达指定分拨中心")
        report.append("")
        report.append("2. 到派一体:")
        report.append("   - 单号输入时长度等于4的代码")
        report.append("   - 用于派送和收件一体化操作")
        report.append("")
        report.append("3. 乡镇驿站扫描:")
        report.append("   - 单号输入时长度等于4的代码")
        report.append("   - 用于乡镇级别的包裹扫描")
        report.append("")
        report.append("4. 集包扫描:")
        report.append("   - 单号输入时开头为9的代码")
        report.append("   - 下一站为预设6位数代码")
        report.append("   - 用于包裹集中打包和转运")
        report.append("")

        # Real tracking numbers analysis
        report.append("真实快递单号分析:")
        report.append("-" * 30)
        report.append("测试单号: 312799099115886, 434640497727147")
        report.append("- 长度: 15位")
        report.append("- 格式: 数字组合")
        report.append("- 首位: 3, 4 (不是9)")
        report.append("- 说明: 这些是标准的15位快递单号，不符合集包扫描的'以9开头'规则")
        report.append("")

        # Encryption Analysis
        encryption_patterns = results.get('encryption_logic', [])
        if encryption_patterns:
            report.append("ENCRYPTION ANALYSIS:")
            report.append("-" * 30)
            for pattern in encryption_patterns[:3]:  # Show first 3
                report.append(f"- {pattern['type']}: {pattern['description']}")
        report.append("")

        # Network Analysis
        network_patterns = results.get('network_requests', [])
        if network_patterns:
            report.append("NETWORK COMMUNICATION ANALYSIS:")
            report.append("-" * 40)
            for pattern in network_patterns[:3]:  # Show first 3
                report.append(f"- {pattern['type']}: {pattern['description']}")
        report.append("")

        # Authentication Format
        auth_patterns = results.get('authentication_format', [])
        if auth_patterns:
            report.append("AUTHENTICATION FORMAT ANALYSIS:")
            report.append("-" * 40)
            report.append("The system appears to use the following authentication components:")
            for pattern in auth_patterns:
                report.append(f"- {pattern['description']}")
        report.append("")

        # Decoder Implementation
        report.append("TRACKING NUMBER DECODER:")
        report.append("-" * 35)
        report.append("A Python decoder has been generated based on the analysis.")
        report.append("Key features:")
        report.append("- Validates 530001 prefix")
        report.append("- Checks 4-digit suffix starting with 9")
        report.append("- Provides detailed error reporting")
        report.append("- Includes test number generation")
        report.append("")

        # Recommendations
        report.append("RECOMMENDATIONS:")
        report.append("-" * 20)
        report.append("1. Use the generated decoder to validate tracking numbers")
        report.append("2. Test with known valid/invalid numbers")
        report.append("3. Monitor network traffic for encryption keys")
        report.append("4. Analyze authentication token generation")
        report.append("5. Reverse engineer password conversion logic")
        report.append("")

        report.append("=" * 80)
        report.append("END OF REPORT")
        report.append("=" * 80)

        return "\n".join(report)

    def save_results(self, results: Dict, output_file: str = "analysis_results.json"):
        """Save analysis results to JSON file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"Results saved to {output_file}")
        except Exception as e:
            print(f"Error saving results: {e}")

    def save_report(self, report: str, output_file: str = "analysis_report.txt"):
        """Save analysis report to text file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"Report saved to {output_file}")
        except Exception as e:
            print(f"Error saving report: {e}")

def main():
    """Main execution function"""
    print("IDA Analysis Script for Logistics System")
    print("=" * 50)

    # Initialize analyzer
    analyzer = LogisticsAnalyzer("all_pseudocode_output.txt")

    # Run analysis
    results = analyzer.run_analysis()

    if 'error' in results:
        print(f"Error: {results['error']}")
        return

    # Generate and display report
    report = analyzer.generate_report(results)
    print(report)

    # Save results
    analyzer.save_results(results)
    analyzer.save_report(report)

    # Generate business logic script file
    business_script = results.get('business_script', '')
    if business_script:
        try:
            with open('business_logic_validator.py', 'w', encoding='utf-8') as f:
                f.write(business_script)
            print("Business logic script saved to business_logic_validator.py")
        except Exception as e:
            print(f"Error saving business script: {e}")

if __name__ == "__main__":
    main()
