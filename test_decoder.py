#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试快递单号解码器
"""

from tracking_number_decoder import decode_tracking_number, generate_valid_tracking_numbers

def test_decoder():
    """测试解码器功能"""
    print("=" * 60)
    print("快递单号解码器测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        # 有效的单号
        ("5300019001", True, "标准有效单号"),
        ("5300019999", True, "最大有效单号"),
        ("5300019123", True, "随机有效单号"),
        
        # 无效的单号 - 前缀错误
        ("5300009001", False, "前缀错误"),
        ("1234569001", False, "完全错误的前缀"),
        
        # 无效的单号 - 后缀错误
        ("5300018001", False, "后缀不以9开头"),
        ("5300017001", False, "后缀以7开头"),
        
        # 无效的单号 - 长度错误
        ("530001900", False, "后缀长度不足"),
        ("53000190001", False, "后缀长度过长"),
        ("530001", False, "没有后缀"),
        
        # 无效的单号 - 包含非数字
        ("5300019abc", False, "包含字母"),
        ("5300019-01", False, "包含特殊字符"),
        
        # 边界情况
        ("5300019000", True, "最小有效后缀"),
        ("", False, "空字符串"),
    ]
    
    print("测试结果:")
    print("-" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for number, expected_valid, description in test_cases:
        result = decode_tracking_number(number)
        actual_valid = result['valid']
        
        status = "✓ 通过" if actual_valid == expected_valid else "✗ 失败"
        print(f"{status} | {number:12} | {description}")
        
        if actual_valid != expected_valid:
            print(f"     期望: {'有效' if expected_valid else '无效'}, 实际: {'有效' if actual_valid else '无效'}")
            if result['errors']:
                print(f"     错误: {', '.join(result['errors'])}")
        
        if actual_valid == expected_valid:
            passed += 1
    
    print("-" * 60)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    # 生成有效单号示例
    print("\n" + "=" * 60)
    print("生成的有效单号示例:")
    print("=" * 60)
    
    valid_numbers = generate_valid_tracking_numbers(10)
    for i, number in enumerate(valid_numbers, 1):
        result = decode_tracking_number(number)
        print(f"{i:2}. {number} - {'有效' if result['valid'] else '无效'}")

def analyze_format():
    """分析单号格式"""
    print("\n" + "=" * 60)
    print("单号格式分析")
    print("=" * 60)
    
    print("根据IDA分析结果:")
    print("- 完整格式: 530001 + 9XXX")
    print("- 前缀: 530001 (6位固定)")
    print("- 后缀: 9XXX (4位，首位必须是9)")
    print("- 总长度: 10位数字")
    print()
    
    print("验证规则:")
    print("1. 必须以 '530001' 开头")
    print("2. 后续4位数字，第一位必须是 '9'")
    print("3. 总长度必须是10位")
    print("4. 只能包含数字")
    print()
    
    print("有效单号范围:")
    print("- 最小: 5300019000")
    print("- 最大: 5300019999")
    print("- 总计: 1000个可能的有效单号")

def main():
    """主函数"""
    test_decoder()
    analyze_format()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
