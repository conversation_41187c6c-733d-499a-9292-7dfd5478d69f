import re
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class MessageFilter:
    """消息筛选器，用于判断消息是否应该被转发"""
    
    def __init__(self, config_file='config.json'):
        """初始化消息筛选器"""
        self.config = self._load_config(config_file)
        self.keywords = self.config.get('keywords', [])
        self.negative_keywords = self.config.get('negative_keywords', [])
        
        # 加载自定义规则
        self.custom_rules = []
        self._load_custom_rules()
        
        logger.info(f"消息筛选器初始化完成，关键词: {self.keywords}, 负面关键词: {self.negative_keywords}")
    
    def _load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {
                'keywords': ["重要", "紧急", "请回复", "通知"],
                'negative_keywords': ["广告", "推销"]
            }
    
    def _load_custom_rules(self):
        """加载自定义规则"""
        # 这里可以从数据库或文件加载更复杂的规则
        # 示例规则：工作时间内的所有消息都转发
        self.custom_rules.append(self._is_work_hours)
        
        # 示例规则：包含@所有人的消息都转发
        self.custom_rules.append(lambda msg, sender: "@所有人" in msg)
    
    def _is_work_hours(self, message, sender):
        """判断当前是否是工作时间（周一至周五 9:00-18:00）"""
        now = datetime.now()
        # 0是周一，6是周日
        is_weekday = now.weekday() < 5
        is_work_hour = 9 <= now.hour < 18
        return is_weekday and is_work_hour
    
    def _contains_keywords(self, message):
        """检查消息是否包含关键词"""
        return any(keyword in message for keyword in self.keywords)
    
    def _contains_negative_keywords(self, message):
        """检查消息是否包含负面关键词"""
        return any(keyword in message for keyword in self.negative_keywords)
    
    def _is_important_by_pattern(self, message):
        """通过模式识别判断消息是否重要"""
        # 包含日期和时间的可能是会议或截止日期提醒
        date_pattern = r'\d{1,2}[/-]\d{1,2}|\d{4}[/-]\d{1,2}[/-]\d{1,2}'
        time_pattern = r'\d{1,2}:\d{2}'
        has_date = re.search(date_pattern, message) is not None
        has_time = re.search(time_pattern, message) is not None
        
        # 包含问号可能是问题或请求
        has_question = '?' in message or '？' in message
        
        # 包含感叹号可能表示重要性
        has_exclamation = '!' in message or '！' in message
        
        return (has_date and has_time) or has_question or has_exclamation
    
    def should_forward(self, message, sender, platform='wechat'):
        """判断消息是否应该被转发"""
        # 如果是钉钉消息且配置为全部转发
        if platform == 'dingtalk' and self.config.get('forward_all_dingtalk', True):
            return True
        
        # 如果包含负面关键词，不转发
        if self._contains_negative_keywords(message):
            logger.debug(f"消息包含负面关键词，不转发: {message}")
            return False
        
        # 如果包含关键词，转发
        if self._contains_keywords(message):
            logger.debug(f"消息包含关键词，转发: {message}")
            return True
        
        # 应用模式识别
        if self._is_important_by_pattern(message):
            logger.debug(f"消息通过模式识别判断为重要，转发: {message}")
            return True
        
        # 应用自定义规则
        for rule in self.custom_rules:
            if rule(message, sender):
                logger.debug(f"消息通过自定义规则判断为重要，转发: {message}")
                return True
        
        logger.debug(f"消息不符合任何转发条件，不转发: {message}")
        return False
    
    def format_message(self, message, sender, platform):
        """格式化消息"""
        template = self.config.get('message_format', {}).get(
            f"{platform}_to_{'wechat' if platform == 'dingtalk' else 'dingtalk'}", 
            "【{platform}-{sender}】: {content}"
        )
        return template.format(sender=sender, content=message, platform=platform)
