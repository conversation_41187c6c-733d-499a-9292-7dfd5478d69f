﻿#Software: 韵达自动化扫描客户端
#ProcessID: 19740 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Microsoft Windows NT 10.0.22000.0, SKY-20220530BOH/Administrator
#CPU: 2
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32765/1000
#Date: 2023-12-20
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
14:06:32.356  1 N - NewLife.Core v10.0.2022.0901 Build 2023-09-01 .NETCoreApp,Version=v6.0
14:06:32.358  1 N - NewLife组件核心库 ©2002-2022 NewLife
14:06:32.360  1 N - 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
14:06:32.360  1 N - 韵达自动化扫描客户端 Copyright ©  2022
14:06:32.360  1 N - 保存配置 D:\韵达客户端\Config\Core.config
14:06:41.783  1 N - AppSet的配置文件D:\韵达客户端\Config\AppSet.config 不存在，准备用默认配置覆盖！

#Software: 韵达自动化扫描客户端
#ProcessID: 22364 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,417M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32759/1000
#Date: 2023-12-20
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
14:10:27.423 17 Y P NewLife.Core v10.0.2022.0901 Build 2023-09-01 .NETCoreApp,Version=v6.0
14:10:27.437 17 Y P NewLife组件核心库 ©2002-2022 NewLife
14:10:27.438 17 Y P 韵达客户端 v1.0.4.6 Build 2000-01-05 00:00:12 .NET 6.0
14:10:27.438 17 Y P 韵达自动化扫描客户端 Copyright ©  2022
14:10:27.438 17 Y P 保存配置 D:\韵达客户端\Config\Socket.config
14:10:27.471 17 Y P KuangScannerNet 准备开始监听4个服务器
14:10:27.528  1 N - XConfig的配置文件D:\韵达客户端\Config\XCoder.config 不存在，准备用默认配置覆盖！
14:10:27.618 17 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
14:10:27.619 17 Y P KuangScannerNet 开始监听 tcp://[::]:12345
14:10:27.623 17 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
14:10:27.635 17 Y P KuangScannerNet 开始监听 udp://[::]:12345
14:10:27.636 17 Y P KuangScannerNet 准备就绪！
14:10:27.647 17 Y P Http 准备开始监听2个服务器
14:10:27.647 17 Y P Http 开始监听 tcp://0.0.0.0:8080
14:10:27.649 17 Y P Http 开始监听 tcp://[::]:8080
14:10:27.649 17 Y P Http 准备就绪！
14:11:27.818 10 Y P YundaExWcs.Config.AppSet的配置文件D:\韵达客户端\Config\AppSet.config有更新，重新加载配置！
14:11:27.820 17 Y P 配置文件D:\韵达客户端\Config\AppSet.config格式不一致，保存为最新格式！
14:12:28.156 14 Y P YundaExWcs.Config.AppSet的配置文件D:\韵达客户端\Config\AppSet.config有更新，重新加载配置！
17:10:26.948  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
17:10:26.948  1 N - 异常退出！
