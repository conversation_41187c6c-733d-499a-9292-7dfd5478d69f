# 快递单号上传脚本分析与实现

## 概述

基于IDA Pro对易语言快递上传程序的逆向分析，提取出核心业务逻辑并用Python重新实现。发现程序支持四种不同的快递操作类型：**到达**、**建包**、**乡镇**、**到派**。

## 关键发现

### 1. 认证信息
从伪代码分析中提取到的关键认证参数：

```
网点编号：530023
业务员工号：9528 (4位数字)
业务员口令：881000 (88开头的6位数字)
分拨中心代码：530001
巴枪SN码：59992231000470 (599开头的14位数字)
```

### 2. 网络通信
- **服务器地址**: `http://scan.yundasys.com:9900/rock`
- **登录接口**: `/query/wdBqLogin/v1`
- **上传接口**: `/upload/outlet/scan/bq/v1`
- **版本号**: `3.6.8.0821`

### 3. 四种操作类型
根据伪代码分析，发现程序支持四种不同的快递操作：

| 操作类型 | 应用类型 | 操作码 | 数据包类型 | 说明 |
|---------|---------|--------|-----------|------|
| **到达** (arrival) | XCRK | 24 | 2(0) | 快递到达网点 |
| **建包** (package) | wddpyt | 13 | 2(1) | 建立包裹 |
| **乡镇** (town) | wddpyt | 13 | 2(1) | 乡镇配送 |
| **到派** (delivery) | wddpyt | 13 | 2(1) | 派送操作 |

### 4. 加密算法
- **算法**: AES-ECB模式
- **密钥**: `k301qsjbrh1s6ega` (16字节)
- **编码**: Base64

### 4. 请求格式

#### 登录请求JSON:
```json
{
  "appType": "1",
  "company": "[网点编号]",
  "device": "[设备信息JSON]",
  "digest": "[数据摘要]",
  "password": "[业务员口令]",
  "reqTime": "[时间戳]",
  "uniqueCode": "[唯一码]",
  "user": "[业务员工号]",
  "version": "3.6.8.0821"
}
```

#### 设备信息JSON:
```json
{
  "imei": "[设备SN]",
  "mac": "[MAC地址]", 
  "serialNumber": "[设备SN]"
}
```

## 使用方法

### 1. 安装依赖
```bash
pip install requests pycryptodome
```

### 2. 运行脚本
```bash
python express_upload_simulator.py
```

### 3. 自定义配置
修改脚本中的认证信息：
```python
self.network_code = "530023"      # 网点编号
self.employee_id = "9528"         # 业务员工号
self.password = "881000"          # 业务员口令
self.center_code = "530001"       # 分拨中心代码
self.device_sn = "59992231000470" # 设备SN
```

## 核心功能

### 1. 设备登录认证
```python
uploader = ExpressUploader()
if uploader.login():
    print("登录成功")
```

### 2. 不同操作类型的单号上传
```python
# 到达操作
uploader.upload_tracking_number("YT1234567890123", "arrival")

# 建包操作
uploader.upload_tracking_number("YT1234567890124", "package")

# 乡镇操作
uploader.upload_tracking_number("YT1234567890125", "town")

# 到派操作
uploader.upload_tracking_number("YT1234567890126", "delivery")
```

### 3. 批量上传（同一操作类型）
```python
tracking_numbers = ["YT1234567890124", "YT1234567890125"]
uploader.batch_upload(tracking_numbers, "arrival")
```

### 4. 混合操作类型上传
```python
tracking_numbers = ["YT1234567890127", "YT1234567890128"]
operation_types = ["arrival", "package"]
uploader.upload_by_operation_type(tracking_numbers, operation_types)
```

### 5. 数据加密/解密
```python
# 加密
encrypted = uploader.aes_encrypt("原始数据")

# 解密
decrypted = uploader.aes_decrypt(encrypted)
```

## 技术细节

### 1. AES加密实现
- 使用ECB模式
- PKCS7填充
- Base64编码输出

### 2. 请求头设置
```python
headers = {
    "User-Agent": "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)",
    "Content-Type": "application/octet-stream",
    "token": "[登录获取的token]"
}
```

### 3. 数据摘要计算
使用MD5算法计算请求数据的摘要值

## 安全注意事项

1. **仅用于学习研究**: 此脚本仅用于技术学习和逆向工程研究
2. **合法使用**: 请确保在合法授权的环境中使用
3. **数据保护**: 注意保护敏感的认证信息
4. **网络安全**: 在安全的网络环境中测试

## 扩展功能

### 1. 添加日志记录
```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
```

### 2. 配置文件支持
```python
import configparser
config = configparser.ConfigParser()
config.read('config.ini')
```

### 3. 错误重试机制
```python
import time
from functools import wraps

def retry(max_attempts=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise e
                    time.sleep(delay)
            return None
        return wrapper
    return decorator
```

## 故障排除

### 1. 登录失败
- 检查网点编号、工号、口令是否正确
- 确认网络连接正常
- 验证服务器地址是否可访问

### 2. 加密失败
- 确认AES密钥正确
- 检查数据格式是否符合要求

### 3. 上传失败
- 确认已成功登录获取token
- 检查快递单号格式是否正确
- 验证网络连接稳定性
