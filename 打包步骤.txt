2. 使用 PyInstaller 打包脚本
使用 PyInstaller 将脚本打包成独立的 .exe 文件。

安装 PyInstaller
在你的开发环境中运行以下命令安装 PyInstaller：

打包命令
运行以下命令将脚本打包成 .exe 文件：

pyinstaller --onefile --windowed --collect-all uiautomator2 自动化工具.py 

--onefile：将所有依赖打包到一个独立的 .exe 文件中。
--noconsole：隐藏控制台窗口（适用于 GUI 应用程序）。
打包完成后，生成的 .exe 文件会位于 dist 文件夹中。

3. 准备分发文件
将以下文件和文件夹打包到一个压缩包中：

打包生成的 .exe 文件（位于 dist 文件夹中）。
platform-tools 文件夹（包含 adb.exe、AdbWinApi.dll 等文件）。
4. 目标电脑的运行步骤
解压压缩包到目标电脑。
确保 platform-tools 文件夹和 .exe 文件在同一个目录下。
双击运行 .exe 文件即可。
5. 测试运行
在没有 Python 环境的电脑上测试生成的 .exe 文件，确保其可以正常运行。如果出现问题：

检查 platform-tools 文件夹是否完整。
确保脚本中正确设置了 PATH 环境变量。
通过上述步骤，你可以将脚本打包成独立的 .exe 文件，并确保其在没有 Python 环境的目标电脑上运行，同时使用本地的 ADB 工具。




AIzaSyAsUJqY2CJprsRSLY1dYovLsM4ir5W4cXQ


console.log("普通日志");       // 白色
console.info("信息日志");      // 蓝色/青色
console.warn("警告日志");      // 黄色
console.error("错误日志");     // 红色
console.verbose("详细日志");   // 灰色
console.log("<font color='#FF0000'>这是红色文本</font>");
console.log("<font color='#00FF00'>这是绿色文本</font>");
console.log("<font color='#0000FF'>这是蓝色文本</font>");
console.log("<font color='red'>红色</font>");
console.log("<font color='green'>绿色</font>");
console.log("<font color='blue'>蓝色</font>");
console.log("<font color='yellow'>黄色</font>");
console.log("<font color='cyan'>青色</font>");
console.log("<font color='magenta'>紫色</font>");
console.warn("<font color='cyan'>这是青色的警告日志</font>");
console.log("<font color='#00FFFF'>当前账号文本数量: " + arr_a.length + "</font>");
console.log("<font color='#FFFF00'>从第 " + (startPos + 1) + " 条开始处理</font>");

