﻿#Software: 韵达自动化扫描客户端
#ProcessID: 5692 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,979M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:22:40.442  5 Y P KuangScannerNet 准备开始监听4个服务器
06:22:40.447  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
06:22:40.454  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
06:22:40.490  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
06:22:40.505  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
06:22:40.506  5 Y P KuangScannerNet 准备就绪！
06:22:40.526  5 Y P Http 准备开始监听2个服务器
06:22:40.529  5 Y P Http 开始监听 tcp://0.0.0.0:8080
06:22:40.538  5 Y P Http 开始监听 tcp://[::]:8080
06:22:40.538  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 8276 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 2,958M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32765/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
06:37:40.842  1 N - NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
06:37:40.844  1 N - NewLife组件核心库 ©2002-2022 NewLife
06:37:40.844  1 N - 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
06:37:40.844  1 N - 韵达自动化扫描客户端 Copyright ©  2022
06:37:40.844  1 N - System.ApplicationException: Object synchronization method was called from an unsynchronized block of code.
   at System.Threading.Mutex.ReleaseMutex()
   at YundaExWcs.Program.Main()
06:37:40.854  1 N - 异常退出！

#Software: 韵达自动化扫描客户端
#ProcessID: 14188 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,712M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:42:03.760 15 Y P KuangScannerNet 准备开始监听4个服务器
13:42:03.845 15 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
13:42:03.939 15 Y P KuangScannerNet 开始监听 tcp://[::]:12345
13:42:03.952 15 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
13:42:03.957 15 Y P KuangScannerNet 开始监听 udp://[::]:12345
13:42:03.957 15 Y P KuangScannerNet 准备就绪！
13:42:03.974 15 Y P Http 准备开始监听2个服务器
13:42:03.982 15 Y P Http 开始监听 tcp://0.0.0.0:8080
13:42:03.989 15 Y P Http 开始监听 tcp://[::]:8080
13:42:03.989 15 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 15636 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 1,709M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
13:42:45.632  5 Y P KuangScannerNet 准备开始监听4个服务器
13:42:45.714  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
13:42:45.737  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
13:42:45.741  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
13:42:45.752  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
13:42:45.752  5 Y P KuangScannerNet 准备就绪！
13:42:45.764  5 Y P Http 准备开始监听2个服务器
13:42:45.765  5 Y P Http 开始监听 tcp://0.0.0.0:8080
13:42:45.765  5 Y P Http 开始监听 tcp://[::]:8080
13:42:45.765  5 Y P Http 准备就绪！

#Software: 韵达自动化扫描客户端
#ProcessID: 10920 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: Console
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 4,881M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32764/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
17:41:48.137  5 Y P KuangScannerNet 准备开始监听4个服务器
17:41:48.142  5 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
17:41:48.143  5 Y P KuangScannerNet 开始监听 tcp://[::]:12345
17:41:48.145  5 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
17:41:48.145  5 Y P KuangScannerNet 开始监听 udp://[::]:12345
17:41:48.145  5 Y P KuangScannerNet 准备就绪！
17:41:48.167  5 Y P Http 准备开始监听2个服务器
17:41:48.168  5 Y P Http 开始监听 tcp://0.0.0.0:8080
17:41:48.168  5 Y P Http 开始监听 tcp://[::]:8080
17:41:48.168  5 Y P Http 准备就绪！
17:45:29.327 15 Y P NewLife.Core v10.0.2022.0901 Build 2024-09-01 .NETCoreApp,Version=v6.0
17:45:29.327 15 Y P NewLife组件核心库 ©2002-2022 NewLife
17:45:29.327 15 Y P 韵达客户端 v1.0.4.8 Build 2000-01-05 00:00:16 .NET 6.0
17:45:29.327 15 Y P 韵达自动化扫描客户端 Copyright ©  2022
17:45:29.327 15 Y P YundaExWcs.Config.AppSet的配置文件D:\韵达客户端\Config\AppSet.config有更新，重新加载配置！

#Software: 韵达自动化扫描客户端
#ProcessID: 12424 x64
#AppDomain: 韵达客户端
#FileName: D:\韵达客户端\韵达客户端.exe
#BaseDirectory: D:\韵达客户端\
#TempPath: C:\Users\<USER>\AppData\Local\Temp\
#CommandLine: D:\韵达客户端\韵达客户端.dll
#ApplicationType: WinForm
#CLR: 6.0.21, .NET 6.0.21
#OS: Windows 11 专业版 10.0.22000, SKY-20220530BOH/Administrator
#CPU: 2
#Memory: 3,583M/8,012M
#Processor: Intel(R) Celeron(R) G5905 CPU @ 3.50GHz
#Product: System Product Name
#Temperature: 27.8
#GC: IsServerGC=False, LatencyMode=Interactive
#ThreadPool: Min=2/2, Max=32767/1000, Available=32760/1000
#Date: 2024-03-01
#详解：https://newlifex.com/core/log
#字段: 时间 线程ID 线程池Y/网页W/普通N/定时T 线程名/任务ID 消息内容
#Fields: Time ThreadID Kind Name Message
19:39:47.653 19 Y P KuangScannerNet 准备开始监听4个服务器
19:39:47.738 19 Y P KuangScannerNet 开始监听 tcp://0.0.0.0:12345
19:39:47.739 19 Y P KuangScannerNet 开始监听 tcp://[::]:12345
19:39:47.777 19 Y P KuangScannerNet 开始监听 udp://0.0.0.0:12345
19:39:47.777 19 Y P KuangScannerNet 开始监听 udp://[::]:12345
19:39:47.777 19 Y P KuangScannerNet 准备就绪！
19:39:47.789 19 Y P Http 准备开始监听2个服务器
19:39:47.810 19 Y P Http 开始监听 tcp://0.0.0.0:8080
19:39:47.811 19 Y P Http 开始监听 tcp://[::]:8080
19:39:47.811 19 Y P Http 准备就绪！
